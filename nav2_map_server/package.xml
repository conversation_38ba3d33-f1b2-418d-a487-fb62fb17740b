<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_map_server</name>
  <version>1.3.1</version>
  <description>
    Refactored map server for ROS2 Navigation
  </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>
  <license>BSD-3-Clause</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>nav2_common</build_depend>

  <depend>rclcpp_lifecycle</depend>
  <depend>nav_msgs</depend>
  <depend>std_msgs</depend>
  <depend>rclcpp</depend>
  <depend>yaml_cpp_vendor</depend>
  <depend>launch_ros</depend>
  <depend>launch_testing</depend>
  <depend>tf2</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_util</depend>
  <depend>graphicsmagick</depend>
  <depend>lifecycle_msgs</depend>

  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_testing</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>

</package>
