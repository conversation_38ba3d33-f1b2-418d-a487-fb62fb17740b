<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="861px" preserveAspectRatio="none" style="width:1525px;height:861px;" version="1.1" viewBox="0 0 1525 861" width="1525px" zoomAndPan="magnify">
    <defs>
        <filter height="300%" id="f1d9y2s1nydwk6" width="300%" x="-1" y="-1">
            <feGaussianBlur result="blurOut" stdDeviation="2.0" />
            <feColorMatrix in="blurOut" result="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 .4 0" />
            <feOffset dx="4.0" dy="4.0" in="blurOut2" result="blurOut3" />
            <feBlend in="SourceGraphic" in2="blurOut3" mode="normal" />
        </filter>
    </defs>
    <g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="169" x="684" y="16.708">DWB Local Planner</text>
        <!--cluster robot_navigation-->
        <polygon fill="#FFFFFF" filter="url(#f1d9y2s1nydwk6)" points="22,44.9531,159,44.9531,166,67.25,1503,67.25,1503,849.9531,22,849.9531,22,44.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="22" x2="166" y1="67.25" y2="67.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="131" x="26" y="59.9482">robot_navigation</text>
        <!--cluster Removed in ROS 2-->
        <path d="M302,94.9531 C302,91.9531 304,89.9531 307,89.9531 C310,89.9531 312,91.9531 312,94.9531 C312,91.9531 314,89.9531 317,89.9531 C320,89.9531 322,91.9531 322,94.9531 C322,91.9531 324,89.9531 327,89.9531 C330,89.9531 332,91.9531 332,94.9531 C332,91.9531 334,89.9531 337,89.9531 C340,89.9531 342,91.9531 342,94.9531 C342,91.9531 344,89.9531 347,89.9531 C350,89.9531 352,91.9531 352,94.9531 C352,91.9531 354,89.9531 357,89.9531 C360,89.9531 362,91.9531 362,94.9531 C362,91.9531 364,89.9531 367,89.9531 C370,89.9531 372,91.9531 372,94.9531 C372,91.9531 374,89.9531 377,89.9531 C380,89.9531 382,91.9531 382,94.9531 C382,91.9531 384,89.9531 387,89.9531 C390,89.9531 392,91.9531 392,94.9531 C392,91.9531 394,89.9531 397,89.9531 C400,89.9531 402,91.9531 402,94.9531 C402,91.9531 404,89.9531 407,89.9531 C410,89.9531 412,91.9531 412,94.9531 C412,91.9531 414,89.9531 417,89.9531 C420,89.9531 422,91.9531 422,94.9531 C422,91.9531 424,89.9531 427,89.9531 C430,89.9531 432,91.9531 432,94.9531 C432,91.9531 434,89.9531 437,89.9531 C440,89.9531 442,91.9531 442,94.9531 C442,91.9531 444,89.9531 447,89.9531 C450,89.9531 452,91.9531 452,94.9531 C452,91.9531 454,89.9531 457,89.9531 C460,89.9531 462,91.9531 462,94.9531 C462,91.9531 464,89.9531 467,89.9531 C470,89.9531 472,91.9531 472,94.9531 C472,91.9531 474,89.9531 477,89.9531 C480,89.9531 482,91.9531 482,94.9531 C482,91.9531 484,89.9531 487,89.9531 C490,89.9531 492,91.9531 492,94.9531 C492,91.9531 494,89.9531 497,89.9531 C500,89.9531 502,91.9531 502,94.9531 C502,91.9531 504,89.9531 507,89.9531 C510,89.9531 512,91.9531 512,94.9531 C512,91.9531 514,89.9531 517,89.9531 C520,89.9531 522,91.9531 522,94.9531 C522,91.9531 524,89.9531 527,89.9531 C530,89.9531 532,91.9531 532,94.9531 C532,91.9531 534,89.9531 537,89.9531 C540,89.9531 542,91.9531 542,94.9531 C542,91.9531 544,89.9531 547,89.9531 C550,89.9531 552,91.9531 552,94.9531 C552,91.9531 554,89.9531 557,89.9531 C560,89.9531 562,91.9531 562,94.9531 C562,91.9531 564,89.9531 567,89.9531 C570,89.9531 572,91.9531 572,94.9531 C572,91.9531 574,89.9531 577,89.9531 C580,89.9531 582,91.9531 582,94.9531 C582,91.9531 584,89.9531 587,89.9531 C590,89.9531 592,91.9531 592,94.9531 C592,91.9531 594,89.9531 597,89.9531 C600,89.9531 602,91.9531 602,94.9531 C602,91.9531 604,89.9531 607,89.9531 C610,89.9531 612,91.9531 612,94.9531 C612,91.9531 614,89.9531 617,89.9531 C620,89.9531 622,91.9531 622,94.9531 C622,91.9531 624,89.9531 627,89.9531 C630,89.9531 632,91.9531 632,94.9531 C632,91.9531 634,89.9531 637,89.9531 C640,89.9531 642,91.9531 642,94.9531 C642,91.9531 644,89.9531 647,89.9531 C650,89.9531 652,91.9531 652,94.9531 C652,91.9531 654,89.9531 657,89.9531 C660,89.9531 662,91.9531 662,94.9531 C662,91.9531 664,89.9531 667,89.9531 C670,89.9531 672,91.9531 672,94.9531 C672,91.9531 674,89.9531 677,89.9531 C680,89.9531 682,91.9531 682,94.9531 C682,91.9531 684,89.9531 687,89.9531 C690,89.9531 692,91.9531 692,94.9531 C692,91.9531 694,89.9531 697,89.9531 C700,89.9531 702,91.9531 702,94.9531 C702,91.9531 704,89.9531 707,89.9531 C710,89.9531 712,91.9531 712,94.9531 C712,91.9531 714,89.9531 717,89.9531 C720,89.9531 722,91.9531 722,94.9531 C722,91.9531 724,89.9531 727,89.9531 C730,89.9531 732,91.9531 732,94.9531 C732,91.9531 734,89.9531 737,89.9531 C740,89.9531 742,91.9531 742,94.9531 C742,91.9531 744,89.9531 747,89.9531 C750,89.9531 752,91.9531 752,94.9531 C752,91.9531 754,89.9531 757,89.9531 C760,89.9531 762,91.9531 762,94.9531 C762,91.9531 764,89.9531 767,89.9531 C770,89.9531 772,91.9531 772,94.9531 C772,91.9531 774,89.9531 777,89.9531 C780,89.9531 782,91.9531 782,94.9531 C782,91.9531 784,89.9531 787,89.9531 C790,89.9531 792,91.9531 792,94.9531 C792,91.9531 794,89.9531 797,89.9531 C800,89.9531 802,91.9531 802,94.9531 C802,91.9531 804,89.9531 807,89.9531 C810,89.9531 812,91.9531 812,94.9531 C812,91.9531 814,89.9531 817,89.9531 C820,89.9531 822,91.9531 822,94.9531 C822,91.9531 824,89.9531 827,89.9531 C830,89.9531 832,91.9531 832,94.9531 C832,91.9531 834,89.9531 837,89.9531 C840,89.9531 842,91.9531 842,94.9531 C842,91.9531 844,89.9531 847,89.9531 C850,89.9531 852,91.9531 852,94.9531 C852,91.9531 854,89.9531 857,89.9531 C860,89.9531 862,91.9531 862,94.9531 C862,91.9531 864,89.9531 867,89.9531 C870,89.9531 872,91.9531 872,94.9531 C872,91.9531 874,89.9531 877,89.9531 C880,89.9531 882,91.9531 882,94.9531 C882,91.9531 884,89.9531 887,89.9531 C890,89.9531 892,91.9531 892,94.9531 C892,91.9531 894,89.9531 897,89.9531 C900,89.9531 902,91.9531 902,94.9531 C902,91.9531 904,89.9531 907,89.9531 C910,89.9531 912,91.9531 912,94.9531 C912,91.9531 914,89.9531 917,89.9531 C920,89.9531 922,91.9531 922,94.9531 C922,91.9531 924,89.9531 927,89.9531 C930,89.9531 932,91.9531 932,94.9531 C932,91.9531 934,89.9531 937,89.9531 C940,89.9531 942,91.9531 942,94.9531 C942,91.9531 944,89.9531 947,89.9531 C950,89.9531 952,91.9531 952,94.9531 C952,91.9531 954,89.9531 957,89.9531 C960,89.9531 962,91.9531 962,94.9531 C962,91.9531 964,89.9531 967,89.9531 C970,89.9531 972,91.9531 972,94.9531 C972,91.9531 974,89.9531 977,89.9531 C980,89.9531 982,91.9531 982,94.9531 C982,91.9531 984,89.9531 987,89.9531 C990,89.9531 992,91.9531 992,94.9531 C992,91.9531 994,89.9531 997,89.9531 C1000,89.9531 1002,91.9531 1002,94.9531 C1002,91.9531 1004,89.9531 1007,89.9531 C1010,89.9531 1012,91.9531 1012,94.9531 C1012,91.9531 1014,89.9531 1017,89.9531 C1020,89.9531 1022,91.9531 1022,94.9531 C1022,91.9531 1024,89.9531 1027,89.9531 C1030,89.9531 1032,91.9531 1032,94.9531 C1032,91.9531 1034,89.9531 1037,89.9531 C1040,89.9531 1042,91.9531 1042,94.9531 C1042,91.9531 1044,89.9531 1047,89.9531 C1050,89.9531 1052,91.9531 1052,94.9531 C1052,91.9531 1054,89.9531 1057,89.9531 C1060,89.9531 1062,91.9531 1062,94.9531 C1062,91.9531 1064,89.9531 1067,89.9531 C1070,89.9531 1072,91.9531 1072,94.9531 C1072,91.9531 1074,89.9531 1077,89.9531 C1080,89.9531 1082,91.9531 1082,94.9531 C1082,91.9531 1084,89.9531 1087,89.9531 C1090,89.9531 1092,91.9531 1092,94.9531 C1092,91.9531 1094,89.9531 1097,89.9531 C1100,89.9531 1102,91.9531 1102,94.9531 C1102,91.9531 1104,89.9531 1107,89.9531 C1110,89.9531 1112,91.9531 1112,94.9531 C1112,91.9531 1114,89.9531 1117,89.9531 C1120,89.9531 1122,91.9531 1122,94.9531 C1122,91.9531 1124,89.9531 1127,89.9531 C1130,89.9531 1132,91.9531 1132,94.9531 C1132,91.9531 1134,89.9531 1137,89.9531 C1140,89.9531 1142,91.9531 1142,94.9531 C1142,91.9531 1144,89.9531 1147,89.9531 C1150,89.9531 1152,91.9531 1152,94.9531 C1155,94.9531 1157,96.9531 1157,99.9531 C1157,102.9531 1155,104.9531 1152,104.9531 C1155,104.9531 1157,106.9531 1157,109.9531 C1157,112.9531 1155,114.9531 1152,114.9531 C1155,114.9531 1157,116.9531 1157,119.9531 C1157,122.9531 1155,124.9531 1152,124.9531 C1155,124.9531 1157,126.9531 1157,129.9531 C1157,132.9531 1155,134.9531 1152,134.9531 C1155,134.9531 1157,136.9531 1157,139.9531 C1157,142.9531 1155,144.9531 1152,144.9531 C1155,144.9531 1157,146.9531 1157,149.9531 C1157,152.9531 1155,154.9531 1152,154.9531 C1155,154.9531 1157,156.9531 1157,159.9531 C1157,162.9531 1155,164.9531 1152,164.9531 C1155,164.9531 1157,166.9531 1157,169.9531 C1157,172.9531 1155,174.9531 1152,174.9531 C1155,174.9531 1157,176.9531 1157,179.9531 C1157,182.9531 1155,184.9531 1152,184.9531 C1155,184.9531 1157,186.9531 1157,189.9531 C1157,192.9531 1155,194.9531 1152,194.9531 C1155,194.9531 1157,196.9531 1157,199.9531 C1157,202.9531 1155,204.9531 1152,204.9531 C1155,204.9531 1157,206.9531 1157,209.9531 C1157,212.9531 1155,214.9531 1152,214.9531 C1155,214.9531 1157,216.9531 1157,219.9531 C1157,222.9531 1155,224.9531 1152,224.9531 C1155,224.9531 1157,226.9531 1157,229.9531 C1157,232.9531 1155,234.9531 1152,234.9531 C1155,234.9531 1157,236.9531 1157,239.9531 C1157,242.9531 1155,244.9531 1152,244.9531 C1155,244.9531 1157,246.9531 1157,249.9531 C1157,252.9531 1155,254.9531 1152,254.9531 C1155,254.9531 1157,256.9531 1157,259.9531 C1157,262.9531 1155,264.9531 1152,264.9531 C1155,264.9531 1157,266.9531 1157,269.9531 C1157,272.9531 1155,274.9531 1152,274.9531 C1155,274.9531 1157,276.9531 1157,279.9531 C1157,282.9531 1155,284.9531 1152,284.9531 C1155,284.9531 1157,286.9531 1157,289.9531 C1157,292.9531 1155,294.9531 1152,294.9531 C1155,294.9531 1157,296.9531 1157,299.9531 C1157,302.9531 1155,304.9531 1152,304.9531 C1155,304.9531 1157,306.9531 1157,309.9531 C1157,312.9531 1155,314.9531 1152,314.9531 C1155,314.9531 1157,316.9531 1157,319.9531 C1157,322.9531 1155,324.9531 1152,324.9531 C1155,324.9531 1157,326.9531 1157,329.9531 C1157,332.9531 1155,334.9531 1152,334.9531 C1155,334.9531 1157,336.9531 1157,339.9531 C1157,342.9531 1155,344.9531 1152,344.9531 C1155,344.9531 1157,346.9531 1157,349.9531 C1157,352.9531 1155,354.9531 1152,354.9531 C1155,354.9531 1157,356.9531 1157,359.9531 C1157,362.9531 1155,364.9531 1152,364.9531 C1155,364.9531 1157,366.9531 1157,369.9531 C1157,372.9531 1155,374.9531 1152,374.9531 C1155,374.9531 1157,376.9531 1157,379.9531 C1157,382.9531 1155,384.9531 1152,384.9531 C1155,384.9531 1157,386.9531 1157,389.9531 C1157,392.9531 1155,394.9531 1152,394.9531 C1152,397.9531 1149,399.9531 1147,399.9531 C1144,399.9531 1142,397.9531 1142,394.9531 C1142,397.9531 1139,399.9531 1137,399.9531 C1134,399.9531 1132,397.9531 1132,394.9531 C1132,397.9531 1129,399.9531 1127,399.9531 C1124,399.9531 1122,397.9531 1122,394.9531 C1122,397.9531 1119,399.9531 1117,399.9531 C1114,399.9531 1112,397.9531 1112,394.9531 C1112,397.9531 1109,399.9531 1107,399.9531 C1104,399.9531 1102,397.9531 1102,394.9531 C1102,397.9531 1099,399.9531 1097,399.9531 C1094,399.9531 1092,397.9531 1092,394.9531 C1092,397.9531 1089,399.9531 1087,399.9531 C1084,399.9531 1082,397.9531 1082,394.9531 C1082,397.9531 1079,399.9531 1077,399.9531 C1074,399.9531 1072,397.9531 1072,394.9531 C1072,397.9531 1069,399.9531 1067,399.9531 C1064,399.9531 1062,397.9531 1062,394.9531 C1062,397.9531 1059,399.9531 1057,399.9531 C1054,399.9531 1052,397.9531 1052,394.9531 C1052,397.9531 1049,399.9531 1047,399.9531 C1044,399.9531 1042,397.9531 1042,394.9531 C1042,397.9531 1039,399.9531 1037,399.9531 C1034,399.9531 1032,397.9531 1032,394.9531 C1032,397.9531 1029,399.9531 1027,399.9531 C1024,399.9531 1022,397.9531 1022,394.9531 C1022,397.9531 1019,399.9531 1017,399.9531 C1014,399.9531 1012,397.9531 1012,394.9531 C1012,397.9531 1009,399.9531 1007,399.9531 C1004,399.9531 1002,397.9531 1002,394.9531 C1002,397.9531 999,399.9531 997,399.9531 C994,399.9531 992,397.9531 992,394.9531 C992,397.9531 989,399.9531 987,399.9531 C984,399.9531 982,397.9531 982,394.9531 C982,397.9531 979,399.9531 977,399.9531 C974,399.9531 972,397.9531 972,394.9531 C972,397.9531 969,399.9531 967,399.9531 C964,399.9531 962,397.9531 962,394.9531 C962,397.9531 959,399.9531 957,399.9531 C954,399.9531 952,397.9531 952,394.9531 C952,397.9531 949,399.9531 947,399.9531 C944,399.9531 942,397.9531 942,394.9531 C942,397.9531 939,399.9531 937,399.9531 C934,399.9531 932,397.9531 932,394.9531 C932,397.9531 929,399.9531 927,399.9531 C924,399.9531 922,397.9531 922,394.9531 C922,397.9531 919,399.9531 917,399.9531 C914,399.9531 912,397.9531 912,394.9531 C912,397.9531 909,399.9531 907,399.9531 C904,399.9531 902,397.9531 902,394.9531 C902,397.9531 899,399.9531 897,399.9531 C894,399.9531 892,397.9531 892,394.9531 C892,397.9531 889,399.9531 887,399.9531 C884,399.9531 882,397.9531 882,394.9531 C882,397.9531 879,399.9531 877,399.9531 C874,399.9531 872,397.9531 872,394.9531 C872,397.9531 869,399.9531 867,399.9531 C864,399.9531 862,397.9531 862,394.9531 C862,397.9531 859,399.9531 857,399.9531 C854,399.9531 852,397.9531 852,394.9531 C852,397.9531 849,399.9531 847,399.9531 C844,399.9531 842,397.9531 842,394.9531 C842,397.9531 839,399.9531 837,399.9531 C834,399.9531 832,397.9531 832,394.9531 C832,397.9531 829,399.9531 827,399.9531 C824,399.9531 822,397.9531 822,394.9531 C822,397.9531 819,399.9531 817,399.9531 C814,399.9531 812,397.9531 812,394.9531 C812,397.9531 809,399.9531 807,399.9531 C804,399.9531 802,397.9531 802,394.9531 C802,397.9531 799,399.9531 797,399.9531 C794,399.9531 792,397.9531 792,394.9531 C792,397.9531 789,399.9531 787,399.9531 C784,399.9531 782,397.9531 782,394.9531 C782,397.9531 779,399.9531 777,399.9531 C774,399.9531 772,397.9531 772,394.9531 C772,397.9531 769,399.9531 767,399.9531 C764,399.9531 762,397.9531 762,394.9531 C762,397.9531 759,399.9531 757,399.9531 C754,399.9531 752,397.9531 752,394.9531 C752,397.9531 749,399.9531 747,399.9531 C744,399.9531 742,397.9531 742,394.9531 C742,397.9531 739,399.9531 737,399.9531 C734,399.9531 732,397.9531 732,394.9531 C732,397.9531 729,399.9531 727,399.9531 C724,399.9531 722,397.9531 722,394.9531 C722,397.9531 719,399.9531 717,399.9531 C714,399.9531 712,397.9531 712,394.9531 C712,397.9531 709,399.9531 707,399.9531 C704,399.9531 702,397.9531 702,394.9531 C702,397.9531 699,399.9531 697,399.9531 C694,399.9531 692,397.9531 692,394.9531 C692,397.9531 689,399.9531 687,399.9531 C684,399.9531 682,397.9531 682,394.9531 C682,397.9531 679,399.9531 677,399.9531 C674,399.9531 672,397.9531 672,394.9531 C672,397.9531 669,399.9531 667,399.9531 C664,399.9531 662,397.9531 662,394.9531 C662,397.9531 659,399.9531 657,399.9531 C654,399.9531 652,397.9531 652,394.9531 C652,397.9531 649,399.9531 647,399.9531 C644,399.9531 642,397.9531 642,394.9531 C642,397.9531 639,399.9531 637,399.9531 C634,399.9531 632,397.9531 632,394.9531 C632,397.9531 629,399.9531 627,399.9531 C624,399.9531 622,397.9531 622,394.9531 C622,397.9531 619,399.9531 617,399.9531 C614,399.9531 612,397.9531 612,394.9531 C612,397.9531 609,399.9531 607,399.9531 C604,399.9531 602,397.9531 602,394.9531 C602,397.9531 599,399.9531 597,399.9531 C594,399.9531 592,397.9531 592,394.9531 C592,397.9531 589,399.9531 587,399.9531 C584,399.9531 582,397.9531 582,394.9531 C582,397.9531 579,399.9531 577,399.9531 C574,399.9531 572,397.9531 572,394.9531 C572,397.9531 569,399.9531 567,399.9531 C564,399.9531 562,397.9531 562,394.9531 C562,397.9531 559,399.9531 557,399.9531 C554,399.9531 552,397.9531 552,394.9531 C552,397.9531 549,399.9531 547,399.9531 C544,399.9531 542,397.9531 542,394.9531 C542,397.9531 539,399.9531 537,399.9531 C534,399.9531 532,397.9531 532,394.9531 C532,397.9531 529,399.9531 527,399.9531 C524,399.9531 522,397.9531 522,394.9531 C522,397.9531 519,399.9531 517,399.9531 C514,399.9531 512,397.9531 512,394.9531 C512,397.9531 509,399.9531 507,399.9531 C504,399.9531 502,397.9531 502,394.9531 C502,397.9531 499,399.9531 497,399.9531 C494,399.9531 492,397.9531 492,394.9531 C492,397.9531 489,399.9531 487,399.9531 C484,399.9531 482,397.9531 482,394.9531 C482,397.9531 479,399.9531 477,399.9531 C474,399.9531 472,397.9531 472,394.9531 C472,397.9531 469,399.9531 467,399.9531 C464,399.9531 462,397.9531 462,394.9531 C462,397.9531 459,399.9531 457,399.9531 C454,399.9531 452,397.9531 452,394.9531 C452,397.9531 449,399.9531 447,399.9531 C444,399.9531 442,397.9531 442,394.9531 C442,397.9531 439,399.9531 437,399.9531 C434,399.9531 432,397.9531 432,394.9531 C432,397.9531 429,399.9531 427,399.9531 C424,399.9531 422,397.9531 422,394.9531 C422,397.9531 419,399.9531 417,399.9531 C414,399.9531 412,397.9531 412,394.9531 C412,397.9531 409,399.9531 407,399.9531 C404,399.9531 402,397.9531 402,394.9531 C402,397.9531 399,399.9531 397,399.9531 C394,399.9531 392,397.9531 392,394.9531 C392,397.9531 389,399.9531 387,399.9531 C384,399.9531 382,397.9531 382,394.9531 C382,397.9531 379,399.9531 377,399.9531 C374,399.9531 372,397.9531 372,394.9531 C372,397.9531 369,399.9531 367,399.9531 C364,399.9531 362,397.9531 362,394.9531 C362,397.9531 359,399.9531 357,399.9531 C354,399.9531 352,397.9531 352,394.9531 C352,397.9531 349,399.9531 347,399.9531 C344,399.9531 342,397.9531 342,394.9531 C342,397.9531 339,399.9531 337,399.9531 C334,399.9531 332,397.9531 332,394.9531 C332,397.9531 329,399.9531 327,399.9531 C324,399.9531 322,397.9531 322,394.9531 C322,397.9531 319,399.9531 317,399.9531 C314,399.9531 312,397.9531 312,394.9531 C312,397.9531 309,399.9531 307,399.9531 C304,399.9531 302,397.9531 302,394.9531 C299,394.9531 297,392.9531 297,389.9531 C297,386.9531 299,384.9531 302,384.9531 C299,384.9531 297,382.9531 297,379.9531 C297,376.9531 299,374.9531 302,374.9531 C299,374.9531 297,372.9531 297,369.9531 C297,366.9531 299,364.9531 302,364.9531 C299,364.9531 297,362.9531 297,359.9531 C297,356.9531 299,354.9531 302,354.9531 C299,354.9531 297,352.9531 297,349.9531 C297,346.9531 299,344.9531 302,344.9531 C299,344.9531 297,342.9531 297,339.9531 C297,336.9531 299,334.9531 302,334.9531 C299,334.9531 297,332.9531 297,329.9531 C297,326.9531 299,324.9531 302,324.9531 C299,324.9531 297,322.9531 297,319.9531 C297,316.9531 299,314.9531 302,314.9531 C299,314.9531 297,312.9531 297,309.9531 C297,306.9531 299,304.9531 302,304.9531 C299,304.9531 297,302.9531 297,299.9531 C297,296.9531 299,294.9531 302,294.9531 C299,294.9531 297,292.9531 297,289.9531 C297,286.9531 299,284.9531 302,284.9531 C299,284.9531 297,282.9531 297,279.9531 C297,276.9531 299,274.9531 302,274.9531 C299,274.9531 297,272.9531 297,269.9531 C297,266.9531 299,264.9531 302,264.9531 C299,264.9531 297,262.9531 297,259.9531 C297,256.9531 299,254.9531 302,254.9531 C299,254.9531 297,252.9531 297,249.9531 C297,246.9531 299,244.9531 302,244.9531 C299,244.9531 297,242.9531 297,239.9531 C297,236.9531 299,234.9531 302,234.9531 C299,234.9531 297,232.9531 297,229.9531 C297,226.9531 299,224.9531 302,224.9531 C299,224.9531 297,222.9531 297,219.9531 C297,216.9531 299,214.9531 302,214.9531 C299,214.9531 297,212.9531 297,209.9531 C297,206.9531 299,204.9531 302,204.9531 C299,204.9531 297,202.9531 297,199.9531 C297,196.9531 299,194.9531 302,194.9531 C299,194.9531 297,192.9531 297,189.9531 C297,186.9531 299,184.9531 302,184.9531 C299,184.9531 297,182.9531 297,179.9531 C297,176.9531 299,174.9531 302,174.9531 C299,174.9531 297,172.9531 297,169.9531 C297,166.9531 299,164.9531 302,164.9531 C299,164.9531 297,162.9531 297,159.9531 C297,156.9531 299,154.9531 302,154.9531 C299,154.9531 297,152.9531 297,149.9531 C297,146.9531 299,144.9531 302,144.9531 C299,144.9531 297,142.9531 297,139.9531 C297,136.9531 299,134.9531 302,134.9531 C299,134.9531 297,132.9531 297,129.9531 C297,126.9531 299,124.9531 302,124.9531 C299,124.9531 297,122.9531 297,119.9531 C297,116.9531 299,114.9531 302,114.9531 C299,114.9531 297,112.9531 297,109.9531 C297,106.9531 299,104.9531 302,104.9531 C299,104.9531 297,102.9531 297,99.9531 C297,96.9531 299,94.9531 302,94.9531 " fill="#DDDDDD" filter="url(#f1d9y2s1nydwk6)" style="stroke: #000000; stroke-width: 1.5;" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="144" x="652.5" y="113.9482">Removed in ROS 2</text>
        <!--cluster nav_core2-->
        <polygon fill="#DDDDDD" filter="url(#f1d9y2s1nydwk6)" points="662,266.9531,749,266.9531,756,289.25,941,289.25,941,375.9531,662,375.9531,662,266.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="662" x2="756" y1="289.25" y2="289.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="81" x="666" y="281.9482">nav_core2</text>
        <!--cluster nav_core_adapter-->
        <polygon fill="#DDDDDD" filter="url(#f1d9y2s1nydwk6)" points="323,130.9531,468,130.9531,475,153.25,1126,153.25,1126,217.9531,323,217.9531,323,130.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="323" x2="475" y1="153.25" y2="153.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="139" x="327" y="145.9482">nav_core_adapter</text>
        <!--cluster dwb_local_planner-->
        <polygon fill="#FFFFFF" filter="url(#f1d9y2s1nydwk6)" points="504,432.9531,654,432.9531,661,455.25,1028,455.25,1028,638.9531,504,638.9531,504,432.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="504" x2="661" y1="455.25" y2="455.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="144" x="508" y="447.9482">dwb_local_planner</text>
        <!--cluster dwb_critics-->
        <polygon fill="#FFFFFF" filter="url(#f1d9y2s1nydwk6)" points="1183,671.9531,1278,671.9531,1285,694.25,1479,694.25,1479,825.9531,1183,825.9531,1183,671.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="1183" x2="1285" y1="694.25" y2="694.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="89" x="1187" y="686.9482">dwb_critics</text>
        <!--cluster dwb_critics <<library>>-->
        <rect fill="#FFFFFF" filter="url(#f1d9y2s1nydwk6)" height="87" style="stroke: #000000; stroke-width: 1.5;" width="248" x="1207" y="714.9531" />
        <rect fill="#FFFFFF" height="10" style="stroke: #000000; stroke-width: 1.5;" width="15" x="1435" y="719.9531" />
        <rect fill="#FFFFFF" height="2" style="stroke: #000000; stroke-width: 1.5;" width="4" x="1433" y="721.9531" />
        <rect fill="#FFFFFF" height="2" style="stroke: #000000; stroke-width: 1.5;" width="4" x="1433" y="725.9531" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="192" x="1235" y="740.9482">dwb_critics &lt;&lt;library&gt;&gt;</text>
        <!--cluster dwb_plugins-->
        <polygon fill="#FFFFFF" filter="url(#f1d9y2s1nydwk6)" points="46,714.9531,150,714.9531,157,737.25,1159,737.25,1159,801.9531,46,801.9531,46,714.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="46" x2="157" y1="737.25" y2="737.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="98" x="50" y="729.9482">dwb_plugins</text>
        <!--cluster costmap_queue-->
        <polygon fill="#FFFFFF" filter="url(#f1d9y2s1nydwk6)" points="48,130.9531,178,130.9531,185,153.25,274,153.25,274,217.9531,48,217.9531,48,130.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="48" x2="185" y1="153.25" y2="153.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="124" x="52" y="145.9482">costmap_queue</text>
        <!--entity GlobalPlanner-->
        <ellipse cx="733" cy="330.9531" fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="95" x="685.5" y="360.9482">GlobalPlanner</text>
        <!--entity LocalPlanner-->
        <ellipse cx="874" cy="330.9531" fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="87" x="830.5" y="360.9482">LocalPlanner</text>
        <!--entity GlobalPlannerAdapter2 <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="242" x="339" y="165.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="334" y="170.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="334" y="192.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="222" x="349" y="188.9482">GlobalPlannerAdapter2 «plugin»</text>
        <!--entity GlobalPlannerAdapter <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="233" x="616.5" y="165.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="611.5" y="170.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="611.5" y="192.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="213" x="626.5" y="188.9482">GlobalPlannerAdapter «plugin»</text>
        <!--entity LocalPlannerAdapter <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="225" x="884.5" y="165.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="879.5" y="170.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="879.5" y="192.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="205" x="894.5" y="188.9482">LocalPlannerAdapter «plugin»</text>
        <!--entity DWBLocalPlanner <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="205" x="519.5" y="467.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="514.5" y="472.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="514.5" y="494.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="185" x="529.5" y="490.9482">DWBLocalPlanner «plugin»</text>
        <!--entity DebugDWBLocalPlanner <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="252" x="760" y="467.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="755" y="472.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="755" y="494.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="232" x="770" y="490.9482">DebugDWBLocalPlanner «plugin»</text>
        <!--entity GoalChecker-->
        <ellipse cx="766" cy="593.9531" fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="89" x="721.5" y="623.9482">GoalChecker</text>
        <!--entity TrajectoryGenerator-->
        <ellipse cx="597" cy="593.9531" fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="138" x="528" y="623.9482">TrajectoryGenerator</text>
        <!--entity TrajectoryCritic-->
        <ellipse cx="932" cy="593.9531" fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="102" x="881" y="623.9482">TrajectoryCritic</text>
        <!--entity Various Path Critics <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="216" x="1223" y="749.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="1218" y="754.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="1218" y="776.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="196" x="1233" y="772.9482">Various Path Critics «plugin»</text>
        <!--entity SimpleGoalChecker <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="219" x="656.5" y="749.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="651.5" y="754.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="651.5" y="776.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="199" x="666.5" y="772.9482">SimpleGoalChecker «plugin»</text>
        <!--entity StoppedGoalChecker <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="232" x="911" y="749.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="906" y="754.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="906" y="776.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="212" x="921" y="772.9482">StoppedGoalChecker «plugin»</text>
        <!--entity StandardTrajectoryGenerator <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="284" x="62" y="749.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="57" y="754.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="57" y="776.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="264" x="72" y="772.9482">StandardTrajectoryGenerator «plugin»</text>
        <!--entity LimitedAccelGenerator <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="240" x="381" y="749.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="376" y="754.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="376" y="776.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="220" x="391" y="772.9482">LimitedAccelGenerator «plugin»</text>
        <!--entity costmap_queue <<library>>-->
        <rect fill="#FEFECE" filter="url(#f1d9y2s1nydwk6)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="194" x="64" y="165.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="59" y="170.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="59" y="192.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="174" x="74" y="188.9482">costmap_queue «library»</text>
        <!--link GlobalPlannerAdapter2 <<plugin>> to GlobalPlanner-->
        <path d="M492.119,202.0131 C551.835,233.7301 677.661,300.5611 719.298,322.6751 " fill="none" id="GlobalPlannerAdapter2 &lt;&lt;plugin&gt;&gt;-GlobalPlanner" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="723.728,325.0291,717.6564,317.2744,719.3124,322.6835,713.9033,324.3394,723.728,325.0291" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link GlobalPlannerAdapter <<plugin>> to GlobalPlanner-->
        <path d="M733,202.1561 C733,231.4311 733,290.3221 733,316.6751 " fill="none" id="GlobalPlannerAdapter &lt;&lt;plugin&gt;&gt;-GlobalPlanner" style="stroke: #A80036; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;" />
        <polygon fill="#A80036" points="733,321.7831,737,312.7831,733,316.7831,729,312.7831,733,321.7831" style="stroke: #A80036; stroke-width: 1.0;" /><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="23" x="734" y="247.02">use</text>
        <!--link LocalPlannerAdapter <<plugin>> to LocalPlanner-->
        <path d="M982.407,202.1561 C957.262,231.7981 906.36,291.8051 884.434,317.6521 " fill="none" id="LocalPlannerAdapter &lt;&lt;plugin&gt;&gt;-LocalPlanner" style="stroke: #A80036; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;" />
        <polygon fill="#A80036" points="880.931,321.7831,889.8033,317.5073,884.1654,317.9702,883.7025,312.3323,880.931,321.7831" style="stroke: #A80036; stroke-width: 1.0;" /><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="23" x="954" y="247.02">use</text>
        <!--link LocalPlanner to DWBLocalPlanner <<plugin>>-->
        <path d="M866.254,344.4081 C855.056,361.3641 832.729,391.4831 806,407.9531 C781.309,423.1671 770.122,414.6841 743,424.9531 C711.204,436.9921 676.608,454.6421 652.585,467.6851 " fill="none" id="LocalPlanner-DWBLocalPlanner &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="869.112,339.9961,860.8611,345.3741,866.3931,344.1923,867.575,349.7243,869.112,339.9961" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link LocalPlanner to DebugDWBLocalPlanner <<plugin>>-->
        <path d="M875.041,345.2201 C877.216,372.9541 882.242,437.0381 884.657,467.8291 " fill="none" id="LocalPlanner-DebugDWBLocalPlanner &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="874.645,340.1791,871.3619,349.4647,875.0365,345.1638,879.3373,348.8383,874.645,340.1791" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link DWBLocalPlanner <<plugin>> to GoalChecker-->
        <path d="M645.136,503.9841 C675.523,526.3521 727.896,564.9041 752.496,583.0121 " fill="none" id="DWBLocalPlanner &lt;&lt;plugin&gt;&gt;-GoalChecker" style="stroke: #A80036; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;" />
        <polygon fill="#A80036" points="756.754,586.1471,751.8785,577.5897,752.7278,583.1824,747.135,584.0317,756.754,586.1471" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link DWBLocalPlanner <<plugin>> to TrajectoryGenerator-->
        <path d="M617.983,503.9841 C612.987,525.1671 604.568,560.8661 600.062,579.9691 " fill="none" id="DWBLocalPlanner &lt;&lt;plugin&gt;&gt;-TrajectoryGenerator" style="stroke: #A80036; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;" />
        <polygon fill="#A80036" points="598.902,584.8911,604.8622,577.0504,600.0504,580.0248,597.076,575.213,598.902,584.8911" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link DWBLocalPlanner <<plugin>> to TrajectoryCritic-->
        <path d="M671.807,503.9841 C744.501,528.8401 875.644,573.6831 918.236,588.2471 " fill="none" id="DWBLocalPlanner &lt;&lt;plugin&gt;&gt;-TrajectoryCritic" style="stroke: #A80036; stroke-width: 1.0; stroke-dasharray: 7.0,7.0;" />
        <polygon fill="#A80036" points="922.981,589.8691,915.7608,583.1707,918.2503,588.2503,913.1707,590.7398,922.981,589.8691" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link TrajectoryCritic to Various Path Critics <<plugin>>-->
        <path d="M946.289,598.0121 C982.755,606.0881 1081.84,629.7411 1159,663.9531 C1214.52,688.5711 1274.27,727.4851 1306.68,749.7671 " fill="none" id="TrajectoryCritic-Various Path Critics &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="941.373,596.9341,949.3069,602.7696,946.2569,598.0055,951.021,594.9554,941.373,596.9341" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link GoalChecker to SimpleGoalChecker <<plugin>>-->
        <path d="M766,608.4991 C766,639.5411 766,715.7661 766,749.9191 " fill="none" id="GoalChecker-SimpleGoalChecker &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="766,603.2491,762,612.2491,766,608.2491,770,612.2491,766,603.2491" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link GoalChecker to StoppedGoalChecker <<plugin>>-->
        <path d="M779.576,603.3291 C797.006,613.9581 828.309,632.6321 856,646.9531 C872.075,655.2671 877.506,654.6021 893,663.9531 C936.46,690.1831 982.376,727.9991 1007.58,749.7641 " fill="none" id="GoalChecker-StoppedGoalChecker &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="775.042,600.5541,780.6301,608.6642,779.3066,603.1643,784.8065,601.8408,775.042,600.5541" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link TrajectoryGenerator to StandardTrajectoryGenerator <<plugin>>-->
        <path d="M582.78,597.6891 C545.606,605.1511 442.95,627.7511 364,663.9531 C311.449,688.0501 255.977,727.3341 226.135,749.7811 " fill="none" id="TrajectoryGenerator-StandardTrajectoryGenerator &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="587.779,596.6981,578.1727,594.5258,582.8746,597.671,579.7293,602.3729,587.779,596.6981" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link TrajectoryGenerator to LimitedAccelGenerator <<plugin>>-->
        <path d="M589.866,607.7361 C572.821,638.2731 529.728,715.4831 510.508,749.9191 " fill="none" id="TrajectoryGenerator-LimitedAccelGenerator &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="592.37,603.2491,584.4904,609.1578,589.9328,607.6149,591.4757,613.0573,592.37,603.2491" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--
@startuml DWB_Structure_Simplified
title DWB Local Planner
package "robot_navigation" {
  cloud "Removed in ROS 2" #DDDDDD {
    package "nav_core2" {
      interface GlobalPlanner
      interface LocalPlanner
    }

    package "nav_core_adapter" {
      [GlobalPlannerAdapter2 <<plugin>>] - -> GlobalPlanner
      [GlobalPlannerAdapter <<plugin>>] ..> GlobalPlanner : use
      [LocalPlannerAdapter <<plugin>>] ..> LocalPlanner : use
    }
  }
  package "dwb_local_planner" {
    [DWBLocalPlanner <<plugin>>] -up-> LocalPlanner
    [DebugDWBLocalPlanner <<plugin>>] -up-> LocalPlanner

    interface GoalChecker
    interface TrajectoryGenerator
    interface TrajectoryCritic

    [DWBLocalPlanner <<plugin>>] .down.> GoalChecker
    [DWBLocalPlanner <<plugin>>] .down.> TrajectoryGenerator
    [DWBLocalPlanner <<plugin>>] .down.> TrajectoryCritic
  }

  package "dwb_critics" {
    component "dwb_critics <<library>>" {
      [Various Path Critics <<plugin>>] -up-> TrajectoryCritic
    }
  }
  package "dwb_plugins" {
    [SimpleGoalChecker <<plugin>>] -up-> GoalChecker
    [StoppedGoalChecker <<plugin>>] -up-> GoalChecker
    [StandardTrajectoryGenerator <<plugin>>] -up-> TrajectoryGenerator
    [LimitedAccelGenerator <<plugin>>] -up-> TrajectoryGenerator

  }

  package "costmap_queue" {
    [costmap_queue <<library>>]
  }
}

@enduml

PlantUML version 1.2017.20(Mon Dec 11 08:57:05 PST 2017)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 1.8.0_181-8u181-b13-0ubuntu0.16.04.1-b13
Operating System: Linux
OS Version: 4.15.0-36-generic
Default Encoding: UTF-8
Language: en
Country: US
-->
    </g>
</svg>