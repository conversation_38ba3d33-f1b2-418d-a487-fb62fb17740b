<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>dwb_core</name>
  <version>1.3.1</version>
  <description>DWB core interfaces package</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD-3-Clause</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>

  <depend>builtin_interfaces</depend>
  <depend>dwb_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>nav2_core</depend>
  <depend>nav2_costmap_2d</depend>
  <depend>nav2_util</depend>
  <depend>nav_2d_msgs</depend>
  <depend>nav_2d_utils</depend>
  <depend>nav_msgs</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>sensor_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>visualization_msgs</depend>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <nav2_core plugin="${prefix}/local_planner_plugin.xml" />
  </export>
</package>
