# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Colcon output
build
log
install

# Visual Studio Code files
.vscode

# Eclipse project files
.cproject
.project
.pydevproject

# Python artifacts
__pycache__/
*.py[cod]
.ipynb_checkpoints

sphinx_doc/_build

# CLion artifacts
.idea
cmake-build-debug/

# doxygen docs
doc/html/
# Vim Swap
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Vim Persistent undo
[._]*.un~

# Vim Session
Session.vim

# Vim Temporary
.netrwhist
