<library path="nav2_rviz_plugins">

  <class name="nav2_rviz_plugins/GoalTool"
         type="nav2_rviz_plugins::GoalTool"
         base_class_type="rviz_common::Tool">
    <description>A tool used to specify the navigation goal pose.</description>
  </class>

  <class name="nav2_rviz_plugins/Navigation 2"
         type="nav2_rviz_plugins::Nav2Panel"
         base_class_type="rviz_common::Panel">
    <description>The Nav2 rviz panel.</description>
  </class>

  <class name="nav2_rviz_plugins/Selector"
         type="nav2_rviz_plugins::Selector"
         base_class_type="rviz_common::Panel">
    <description>The Nav2 rviz panel for selecting planners and controllers.</description>
  </class>

  <class name="nav2_rviz_plugins/Docking"
         type="nav2_rviz_plugins::DockingPanel"
         base_class_type="rviz_common::Panel">
    <description>The Nav2 rviz panel for dock and undock actions.</description>
  </class>

  <class name="nav2_rviz_plugins/CostmapCostTool"
         type="nav2_rviz_plugins::CostmapCostTool"
         base_class_type="rviz_common::Tool">
    <description>A Nav2 tool for getting the cost of point in costmap.</description>
  </class>

  <class name="nav2_rviz_plugins/ParticleCloud"
         type="nav2_rviz_plugins::ParticleCloudDisplay"
         base_class_type="rviz_common::Display">
    <description>The Particle Cloud rviz display.</description>
  </class>

</library>
