<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_lifecycle_manager</name>
  <version>1.3.1</version>
  <description>A controller/manager for the lifecycle nodes of the Navigation 2 system</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>

  <depend>bondcpp</depend>
  <depend>diagnostic_updater</depend>
  <depend>geometry_msgs</depend>
  <depend>lifecycle_msgs</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_util</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_action</depend>
  <depend>rclcpp_components</depend>
  <depend>std_srvs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>rclcpp_lifecycle</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
