
#goal definition
geometry_msgs/Point target
float32 speed
builtin_interfaces/Duration time_allowance
bool disable_collision_checks False
---
#result definition

# Error codes
# Note: The expected priority order of the error should match the message order
uint16 NONE=0
uint16 UNKNOWN=710
uint16 TIMEOUT=711
uint16 TF_ERROR=712
uint16 INVALID_INPUT=713
uint16 COLLISION_AHEAD=714

builtin_interfaces/Duration total_elapsed_time
uint16 error_code
string error_msg
---
#feedback definition
float32 distance_traveled
