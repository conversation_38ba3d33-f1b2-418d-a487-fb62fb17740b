#goal definition
nav_msgs/Path path
string controller_id
string goal_checker_id
string progress_checker_id
---
#result definition

# Error codes
# Note: The expected priority order of the errors should match the message order
uint16 NONE=0
uint16 UNKNOWN=100
uint16 INVALID_CONTROLLER=101
uint16 TF_ERROR=102
uint16 INVALID_PATH=103
uint16 PATIENCE_EXCEEDED=104
uint16 FAILED_TO_MAKE_PROGRESS=105
uint16 NO_VALID_CONTROL=106
uint16 CONTROLLER_TIMED_OUT=107

std_msgs/Empty result
uint16 error_code
string error_msg
---
#feedback definition
float32 distance_to_goal
float32 speed
