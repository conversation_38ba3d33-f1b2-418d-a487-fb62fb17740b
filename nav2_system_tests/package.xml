<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_system_tests</name>
  <version>1.3.1</version>
  <description>A sets of system-level tests for Nav2 usually involving full robot simulation</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>
  <build_depend>rosidl_default_generators</build_depend>

  <exec_depend>rosidl_default_runtime</exec_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>ament_index_cpp</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>geometry_msgs</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_ros</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>lcov</test_depend>
  <test_depend>nav2_amcl</test_depend>
  <test_depend>nav2_behavior_tree</test_depend>
  <test_depend>nav2_bringup</test_depend>
  <test_depend>nav2_lifecycle_manager</test_depend>
  <test_depend>nav2_map_server</test_depend>
  <test_depend>nav2_minimal_tb3_sim</test_depend>
  <test_depend>nav2_msgs</test_depend>
  <test_depend>nav2_navfn_planner</test_depend>
  <test_depend>nav2_planner</test_depend>
  <test_depend>nav2_util</test_depend>
  <test_depend>nav_msgs</test_depend>
  <test_depend>navigation2</test_depend>
  <test_depend>python3-zmq</test_depend>
  <test_depend>rclcpp</test_depend>
  <test_depend>rclcpp_lifecycle</test_depend>
  <test_depend>rclpy</test_depend>
  <test_depend>robot_state_publisher</test_depend>
  <test_depend>std_msgs</test_depend>
  <test_depend>tf2</test_depend>
  <test_depend>tf2_geometry_msgs</test_depend>
  <test_depend>tf2_ros</test_depend>
  <test_depend>visualization_msgs</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <nav2_core plugin="${prefix}/src/error_codes/controller_plugins.xml"/>
    <nav2_core plugin="${prefix}/src/error_codes/planner_plugins.xml" />
    <nav2_core plugin="${prefix}/src/error_codes/smoother_plugins.xml" />
  </export>
</package>
