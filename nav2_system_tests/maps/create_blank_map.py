#!/usr/bin/env python3

from PIL import Image
import numpy as np
import os

# 设置地图参数
resolution = 0.5  # 每像素代表0.5米，这样可以减小文件大小
width_meters = 1000  # 地图宽度（米）
height_meters = 1000  # 地图高度（米）

# 计算像素尺寸
width_pixels = int(width_meters / resolution)
height_pixels = int(height_meters / resolution)

print(f"创建 {width_meters}m x {height_meters}m 的空白地图，分辨率: {resolution}m/pixel")
print(f"图像尺寸: {width_pixels} x {height_pixels} 像素")

# 创建一个全白的图像（255表示空闲空间）
blank_map = np.ones((height_pixels, width_pixels), dtype=np.uint8) * 255

# 保存为PNG图像
output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "blank_map_1000x1000.png")
Image.fromarray(blank_map).save(output_path)

print(f"空白地图已保存到: {output_path}")

# 创建对应的YAML文件
yaml_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "blank_map_1000x1000.yaml")
with open(yaml_path, 'w') as yaml_file:
    yaml_file.write(f"image: {os.path.basename(output_path)}\n")
    yaml_file.write(f"resolution: {resolution}\n")
    yaml_file.write(f"origin: [-500.0, -500.0, 0.0]\n")  # 原点在地图中心
    yaml_file.write(f"occupied_thresh: 0.65\n")
    yaml_file.write(f"free_thresh: 0.25\n")
    yaml_file.write(f"negate: 0\n")
    yaml_file.write(f"mode: trinary\n")

print(f"YAML配置文件已保存到: {yaml_path}")
