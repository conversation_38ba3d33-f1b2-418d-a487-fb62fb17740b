controller_server:
  ros__parameters:
    controller_frequency: 20.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.5
    min_theta_velocity_threshold: 0.2
    failure_tolerance: -0.1
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["general_goal_checker"] # "precise_goal_checker"
    controller_plugins: [ "unknown", "tf_error", "invalid_path", "patience_exceeded","failed_to_make_progress", "no_valid_control"]
    unknown:
      plugin: "nav2_system_tests::UnknownErrorController"
    tf_error:
      plugin: "nav2_system_tests::TFErrorController"
    invalid_path:
      plugin: "nav2_system_tests::InvalidPathErrorController"
    patience_exceeded:
      plugin: "nav2_system_tests::PatienceExceededErrorController"
    failed_to_make_progress:
      plugin: "nav2_system_tests::FailedToMakeProgressErrorController"
    no_valid_control:
      plugin: "nav2_system_tests::NoValidControlErrorController"


    # Progress checker parameters
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      movement_time_allowance: 10.0
    # Goal checker parameters
    #precise_goal_checker:
    #  plugin: "nav2_controller::SimpleGoalChecker"
    #  xy_goal_tolerance: 0.25
    #  yaw_goal_tolerance: 0.25
    #  stateful: True
    general_goal_checker:
      stateful: True
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.25
      yaw_goal_tolerance: 0.25
    # DWB parameters
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"
      debug_trajectory_details: True
      min_vel_x: 0.0
      min_vel_y: 0.0
      max_vel_x: 0.26
      max_vel_y: 0.0
      max_vel_theta: 1.0
      min_speed_xy: 0.0
      max_speed_xy: 0.26
      min_speed_theta: 0.0
      # Add high threshold velocity for turtlebot 3 issue.
      # https://github.com/ROBOTIS-GIT/turtlebot3_simulations/issues/75
      acc_lim_x: 2.5
      acc_lim_y: 0.0
      acc_lim_theta: 3.2
      decel_lim_x: -2.5
      decel_lim_y: 0.0
      decel_lim_theta: -3.2
      vx_samples: 20
      vy_samples: 5
      vtheta_samples: 20
      sim_time: 1.7
      linear_granularity: 0.05
      angular_granularity: 0.025
      transform_tolerance: 0.2
      xy_goal_tolerance: 0.25
      trans_stopped_velocity: 0.25
      short_circuit_trajectory_evaluation: True
      stateful: True
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]
      BaseObstacle.scale: 0.02
      PathAlign.scale: 32.0
      PathAlign.forward_point_distance: 0.1
      GoalAlign.scale: 24.0
      GoalAlign.forward_point_distance: 0.1
      PathDist.scale: 32.0
      GoalDist.scale: 24.0
      RotateToGoal.scale: 32.0
      RotateToGoal.slowing_factor: 5.0
      RotateToGoal.lookahead_time: -1.0

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 5.0
      publish_frequency: 2.0
      global_frame: odom
      robot_base_frame: base_link
      rolling_window: true
      width: 3
      height: 3
      resolution: 0.05
      robot_radius: 0.22
      plugins: ["voxel_layer", "inflation_layer"]
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      always_send_full_costmap: True

global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 1.0
      publish_frequency: 1.0
      global_frame: map
      robot_base_frame: base_link
      robot_radius: 0.22
      resolution: 0.05
      track_unknown_space: true
      plugins: ["obstacle_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0

planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    planner_plugins: [ "unknown", "tf_error", "start_outside_map", "goal_outside_map",
                       "start_occupied", "goal_occupied", "timeout","no_valid_path",
                       "no_viapoints_given", "cancelled" ]
    unknown:
      plugin: "nav2_system_tests::UnknownErrorPlanner"
    tf_error:
      plugin: "nav2_system_tests::TFErrorPlanner"
    start_outside_map:
      plugin: "nav2_system_tests::StartOutsideMapErrorPlanner"
    goal_outside_map:
      plugin: "nav2_system_tests::GoalOutsideMapErrorPlanner"
    start_occupied:
      plugin: "nav2_system_tests::StartOccupiedErrorPlanner"
    goal_occupied:
      plugin: "nav2_system_tests::GoalOccupiedErrorPlanner"
    timeout:
      plugin: "nav2_system_tests::TimedOutErrorPlanner"
    no_valid_path:
      plugin: "nav2_system_tests::NoValidPathErrorPlanner"
    no_viapoints_given:
      plugin: "nav2_system_tests::NoViapointsGivenErrorPlanner"
    cancelled:
      plugin: "nav2_system_tests::CancelledPlanner"

smoother_server:
  ros__parameters:
    smoother_plugins: ["unknown", "timeout", "smoothed_path_in_collision",
                       "failed_to_smooth_path", "invalid_path"]
    unknown:
      plugin: "nav2_system_tests::UnknownErrorSmoother"
    timeout:
      plugin: "nav2_system_tests::TimeOutErrorSmoother"
    smoothed_path_in_collision:
      plugin: "nav2_system_tests::SmoothedPathInCollision"
    failed_to_smooth_path:
      plugin: "nav2_system_tests::FailedToSmoothPath"
    invalid_path:
      plugin: "nav2_system_tests::InvalidPath"
