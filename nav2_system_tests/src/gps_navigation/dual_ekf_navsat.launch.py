# Copyright 2018 Open Source Robotics Foundation, Inc.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# 中文说明：这是一个用于启动双EKF和GPS导航转换的启动文件

import os

# 导入必要的ROS2启动相关模块
from launch import LaunchDescription  # 用于创建启动描述
import launch.actions  # 启动动作
import launch_ros.actions  # ROS特定的启动动作


def generate_launch_description() -> LaunchDescription:
    # 生成启动描述，定义如何启动双EKF和GPS导航转换节点
    # 获取当前文件目录和参数文件路径
    launch_dir = os.path.dirname(os.path.realpath(__file__))  # 获取当前文件目录
    params_file = os.path.join(launch_dir, 'dual_ekf_navsat_params.yaml')  # 参数文件路径
    os.environ['FILE_PATH'] = str(launch_dir)  # 设置环境变量

    # 定义robot_localization生命周期包装器节点名称
    robot_localization_lifecycle_nodes = [
        'robot_localization_lifecycle_wrapper'
    ]

    # 返回启动描述，包含所有需要启动的节点
    return LaunchDescription(
        [
            # 声明启动参数：是否输出最终位置
            launch.actions.DeclareLaunchArgument(
                'output_final_position', default_value='false'  # 默认不输出最终位置
            ),
            # 声明启动参数：输出位置
            launch.actions.DeclareLaunchArgument(
                'output_location', default_value='~/dual_ekf_navsat_example_debug.txt'  # 调试文件路径
            ),
            # 声明启动参数：是否自动启动
            launch.actions.DeclareLaunchArgument(
                'autostart', default_value='true'  # 默认自动启动
            ),

            # 启动robot_localization生命周期包装器节点
            # 这个节点是生命周期节点，可以被lifecycle_manager管理
            # 它负责启动和管理robot_localization包中的EKF和navsat_transform节点
            launch_ros.actions.Node(
                package='nav2_system_tests',  # 包名
                executable='robot_localization_lifecycle_wrapper.py',  # 可执行文件
                name='robot_localization_lifecycle_wrapper',  # 节点名
                output='screen',  # 输出到屏幕
                parameters=[
                    {'params_file': params_file},  # 参数文件路径
                    {'use_sim_time': True}  # 使用仿真时间
                ],
            ),

            # 添加生命周期管理器来管理robot_localization包装器节点
            launch_ros.actions.Node(
                package='nav2_lifecycle_manager',
                executable='lifecycle_manager',
                name='lifecycle_manager_robot_localization',
                output='screen',
                parameters=[
                    {'use_sim_time': True},
                    {'autostart': True},
                    {'node_names': robot_localization_lifecycle_nodes}
                ]
            ),
        ]
    )
