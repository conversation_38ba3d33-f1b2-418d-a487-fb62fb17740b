# 室内区域和出入口配置
indoor_areas:
  - id: "building_1"
    name: "主楼"
    # 出入口定义
    entrances:
      - id: "entrance_1_1"
        name: "主入口"
        # 入口点定义 - 室内侧点(P1)和室外侧点(P2)
        # GPS坐标基于北京世界原点，保持与原爱丁堡世界原点相同的相对偏移量
        # 原世界原点：(55.944831, -3.186998) -> 新世界原点：(39.788888, 116.099178)
        points:
          inner:  # P1 - 室内侧点
            gps:
              lat: 39.788866  # 39.788888 + (55.944809 - 55.944831) = 39.788888 + (-0.000022)
              lon: 116.099200  # 116.099178 + (-3.186976 - (-3.186998)) = 116.099178 + 0.000022
            global_pose:  # 门线点在室内地图中的坐标
              x: 0.10785
              y: -0.89171
              yaw: 0.0  # 朝向角（弧度），0表示朝向X轴正方向
          outer:  # P2 - 室外侧点
            gps:
              lat: 39.788859  # 39.788888 + (55.94480192 - 55.944831) = 39.788888 + (-0.00002908)
              lon: 116.099198  # 116.099178 + (-3.18697760 - (-3.186998)) = 116.099178 + 0.00002040
            global_pose:  # 门线点在室内地图中的坐标
              x: 0.02956
              y: -1.78595
              yaw: 0.0  # 朝向角（弧度）
        # 门线宽度（垂直于P1P2方向的线段长度，单位：米）
        door_width: 4.0
        # 室内地图文件路径
        map_file: "/opt/overlay_ws/src/navigation2/nav2_minimal_tb3_sim/nav2_minimal_tb3_sim/launch/house.yaml"

