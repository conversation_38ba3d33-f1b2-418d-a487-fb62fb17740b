#!/usr/bin/env python3
# Copyright (c) 2023 Your Organization
# Licensed under the Apache License, Version 2.0

"""
地图管理器节点，用于管理多个室内地图和坐标转换。
负责在出入口处加载对应的室内地图，并处理全局地图和室内地图之间的坐标转换。
"""

import rclpy
from rclpy.node import Node
from nav2_msgs.srv import LoadMap, ManageLifecycleNodes
from geometry_msgs.msg import TransformStamped, PoseWithCovarianceStamped
from nav2_system_tests.msg import EnvInfo
from nav_msgs.msg import OccupancyGrid
from lifecycle_msgs.srv import GetState, ChangeState
from lifecycle_msgs.msg import Transition
import tf2_ros
import yaml
import os
import math
import subprocess
from tf_transformations import quaternion_from_euler
from rcl_interfaces.srv import SetParameters
from rcl_interfaces.msg import Parameter, ParameterValue, ParameterType


class MapManagerNode(Node):
    """地图管理器节点，负责管理多个室内地图和坐标转换。"""

    def __init__(self):
        """初始化地图管理器节点。"""
        super().__init__('map_manager')

        # 声明参数
        self.declare_parameter('indoor_areas_config', '')
        self.declare_parameter('map_update_interval', 1.0)
        self.declare_parameter('tf_broadcast_interval', 0.1)
        self.declare_parameter('debug_level', 'debug')  # 调试级别: debug, info, warn，默认设为debug以获取更多信息
        self.declare_parameter('blank_map_path', '')  # 空白地图路径
        # 获取参数值
        self.config_path = self.get_parameter('indoor_areas_config').value
        self.map_update_interval = self.get_parameter('map_update_interval').value
        self.tf_broadcast_interval = self.get_parameter('tf_broadcast_interval').value
        self.debug_level = self.get_parameter('debug_level').value
        self.blank_map_path = self.get_parameter('blank_map_path').value

        # 设置日志级别
        if self.debug_level == 'debug':
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.DEBUG)
        elif self.debug_level == 'info':
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.INFO)
        elif self.debug_level == 'warn':
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.WARN)
        else:
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.INFO)

        # 输出初始化信息
        self.get_logger().info('地图管理器节点初始化中...')
        self.get_logger().info(f'室内区域配置文件: {self.config_path}')
        self.get_logger().info(f'空白地图路径: {self.blank_map_path}')
        self.get_logger().info(f'调试级别: {self.debug_level}')

        # 检查配置文件路径
        if not self.config_path:
            self.get_logger().error('未指定室内区域配置文件路径')
        elif not os.path.exists(self.config_path):
            self.get_logger().error(f'室内区域配置文件不存在: {self.config_path}')
        else:
            self.get_logger().info(f'室内区域配置文件存在: {self.config_path}')

        # 检查空白地图路径
        if not self.blank_map_path:
            self.get_logger().error('未指定空白地图路径')
        elif not os.path.exists(self.blank_map_path):
            self.get_logger().error(f'空白地图文件不存在: {self.blank_map_path}')
        else:
            self.get_logger().info(f'空白地图文件存在: {self.blank_map_path}')

        # 加载室内区域配置
        self.indoor_areas = self.load_indoor_areas_config()
        self.get_logger().info(f'加载了 {len(self.indoor_areas)} 个室内区域')

        # 创建服务客户端
        self.load_map_client = self.create_client(LoadMap, 'map_server/load_map')
        self.load_map_available = False  # 用于跟踪服务是否可用

        # 创建导航生命周期管理器服务客户端
        self.nav_lifecycle_client = self.create_client(
            ManageLifecycleNodes, 'lifecycle_manager_navigation/manage_nodes')
        self.nav_lifecycle_available = False  # 用于跟踪服务是否可用

        # 创建定位生命周期管理器服务客户端（用于管理robot_localization包装器）
        self.localization_lifecycle_client = self.create_client(
            ManageLifecycleNodes, 'lifecycle_manager_localization/manage_nodes')
        self.localization_lifecycle_available = False  # 用于跟踪服务是否可用

        # TF监控相关
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        self.tf_check_timer = None  # TF检查定时器
        self.navigation_startup_pending = False  # 是否有待启动的导航系统

        # 创建环境信息订阅者
        self.env_info_sub = self.create_subscription(
            EnvInfo,
            'environment_info',
            self.environment_info_callback,
            10)
        self.get_logger().info('已创建环境信息订阅者，主题: environment_info')

        # 初始化状态变量
        self.current_environment = 'unknown'
        self.current_area_id = ''
        self.current_entrance_id = ''
        self.current_map_file = ''
        self.current_transform = None
        self.previous_environment = 'unknown'  # 用于跟踪环境变化

        # TF发布状态跟踪
        self.amcl_tf_enabled = False  # 用于跟踪AMCL的TF发布状态，默认禁用
        self.ekf_odom_tf_enabled = False  # 用于跟踪EKF odom的TF发布状态，默认禁用
        self.ekf_map_tf_enabled = False  # 用于跟踪EKF map的TF发布状态，默认禁用
        self.navsat_tf_enabled = False  # 用于跟踪NavSat Transform的TF发布状态，默认禁用
        self.ekf_indoor_tf_enabled = False  # 用于跟踪室内EKF的TF发布状态，默认禁用

        # 环境切换操作跟踪
        self.is_switching_to_indoor = False  # 是否正在切换到室内环境
        self.is_switching_to_outdoor = False  # 是否正在切换到室外环境
        self.pending_entrance_info = None  # 待处理的入口信息，用于地图加载

        # 创建参数设置服务客户端
        self.amcl_param_client = self.create_client(
            SetParameters, 'amcl/set_parameters')
        self.ekf_odom_param_client = self.create_client(
            SetParameters, 'ekf_filter_node_odom/set_parameters')
        self.ekf_map_param_client = self.create_client(
            SetParameters, 'ekf_filter_node_map/set_parameters')
        self.navsat_param_client = self.create_client(
            SetParameters, 'navsat_transform/set_parameters')
        # 添加室内EKF参数设置服务客户端
        self.ekf_indoor_param_client = self.create_client(
            SetParameters, 'ekf_filter_node_indoor/set_parameters')

        # 创建AMCL初始位置话题发布者
        from geometry_msgs.msg import PoseWithCovarianceStamped
        self.amcl_initial_pose_pub = self.create_publisher(
            PoseWithCovarianceStamped, 'initialpose', 10)

        # 创建AMCL和室内EKF包装器的生命周期客户端
        self.amcl_lifecycle_client = self.create_client(
            ChangeState, 'amcl/change_state')
        self.indoor_ekf_lifecycle_client = self.create_client(
            ChangeState, 'indoor_ekf_lifecycle_wrapper/change_state')

        self.get_logger().info('地图管理器节点初始化完成')

        # 等待服务可用
        self.wait_for_services()

    def set_amcl_initial_pose(self):
        """通过话题设置AMCL的初始位置，基于当前入口信息"""
        try:
            if not self.pending_entrance_info:
                self.get_logger().warning('没有入口信息，无法设置AMCL初始位置')
                return False

            # 获取室内侧点的坐标作为初始位置
            if 'points' in self.pending_entrance_info and 'inner' in self.pending_entrance_info['points']:
                inner_point = self.pending_entrance_info['points']['inner']['global_pose']

                # 创建初始位置消息
                from geometry_msgs.msg import PoseWithCovarianceStamped
                pose_msg = PoseWithCovarianceStamped()
                pose_msg.header.frame_id = 'map'
                pose_msg.header.stamp = self.get_clock().now().to_msg()

                # 设置位置
                pose_msg.pose.pose.position.x = float(inner_point['x'])
                pose_msg.pose.pose.position.y = float(inner_point['y'])
                pose_msg.pose.pose.position.z = 0.0

                # 设置方向（四元数）
                from tf_transformations import quaternion_from_euler
                yaw = float(inner_point.get('yaw', 0.0))
                quat = quaternion_from_euler(0, 0, yaw)
                pose_msg.pose.pose.orientation.x = quat[0]
                pose_msg.pose.pose.orientation.y = quat[1]
                pose_msg.pose.pose.orientation.z = quat[2]
                pose_msg.pose.pose.orientation.w = quat[3]

                # 设置协方差矩阵
                pose_msg.pose.covariance = [
                    0.25, 0.0, 0.0, 0.0, 0.0, 0.0,  # x
                    0.0, 0.25, 0.0, 0.0, 0.0, 0.0,  # y
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # z
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # roll
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # pitch
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.068  # yaw
                ]

                self.get_logger().info(f'通过话题设置AMCL初始位置: x={pose_msg.pose.pose.position.x}, '
                                     f'y={pose_msg.pose.pose.position.y}, yaw={yaw}')

                # 发布初始位置消息
                self.amcl_initial_pose_pub.publish(pose_msg)

                return True
            else:
                self.get_logger().error('入口信息中缺少室内侧点坐标')
                return False

        except Exception as e:
            self.get_logger().error(f'通过话题设置AMCL初始位置时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            return False





    def _delayed_set_amcl_initial_pose(self):
        """延迟设置AMCL初始位置（带重试机制的定时器回调）"""
        try:
            # 检查重试次数
            if not hasattr(self, '_initial_pose_retry_count'):
                self._initial_pose_retry_count = 0

            self._initial_pose_retry_count += 1
            max_retries = 5

            self.get_logger().info(f'尝试设置AMCL初始位置 (第{self._initial_pose_retry_count}次/共{max_retries}次)')

            # 设置初始位置
            success = self.set_amcl_initial_pose()
            if success:
                self.get_logger().info('延迟设置AMCL初始位置请求已发送')
                # 成功后销毁定时器
                if hasattr(self, '_initial_pose_timer'):
                    self._initial_pose_timer.cancel()
                    self._initial_pose_timer = None
            else:
                self.get_logger().warning(f'延迟设置AMCL初始位置失败 (第{self._initial_pose_retry_count}次)')

                # 如果还有重试次数，继续重试
                if self._initial_pose_retry_count < max_retries:
                    self.get_logger().info(f'将在2秒后重试设置AMCL初始位置')
                    # 不需要重新创建定时器，让当前定时器继续运行
                    # 但需要调整定时器间隔为2秒
                    if hasattr(self, '_initial_pose_timer'):
                        self._initial_pose_timer.cancel()
                    self._initial_pose_timer = self.create_timer(2.0, self._delayed_set_amcl_initial_pose)
                else:
                    self.get_logger().error(f'设置AMCL初始位置失败，已达到最大重试次数({max_retries})')
                    # 销毁定时器
                    if hasattr(self, '_initial_pose_timer'):
                        self._initial_pose_timer.cancel()
                        self._initial_pose_timer = None

        except Exception as e:
            self.get_logger().error(f'延迟设置AMCL初始位置时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            # 发生异常时也销毁定时器
            if hasattr(self, '_initial_pose_timer'):
                self._initial_pose_timer.cancel()
                self._initial_pose_timer = None

    def wait_for_services(self):
        """等待所有服务可用，如果某些服务不可用，记录警告但继续运行。"""
        # 检查加载地图服务是否可用
        self.get_logger().info('Waiting for load_map service...')
        if self.load_map_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().info('Load map service is available')
            self.load_map_available = True
        else:
            self.get_logger().warning('Load map service is NOT available, map loading functionality will be limited')
            self.load_map_available = False

        # 检查导航生命周期管理器服务是否可用
        self.get_logger().info('Waiting for navigation lifecycle manager service...')
        if self.nav_lifecycle_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().info('Navigation lifecycle manager service is available')
            self.nav_lifecycle_available = True
        else:
            self.get_logger().warning('Navigation lifecycle manager service is NOT available, navigation pause/resume functionality will be limited')
            self.nav_lifecycle_available = False

        # 检查定位生命周期管理器服务是否可用
        self.get_logger().info('Waiting for localization lifecycle manager service...')
        if self.localization_lifecycle_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().info('Localization lifecycle manager service is available')
            self.localization_lifecycle_available = True
        else:
            self.get_logger().warning('Localization lifecycle manager service is NOT available, robot_localization control functionality will be limited')
            self.localization_lifecycle_available = False

        # 检查AMCL参数服务是否可用
        self.get_logger().info('Waiting for AMCL parameter service...')
        if self.amcl_param_client.wait_for_service(timeout_sec=2.0):
            self.get_logger().info('AMCL parameter service is available')
        else:
            self.get_logger().warning('AMCL parameter service is NOT available, TF control functionality will be limited')

        # 检查EKF odom参数服务是否可用
        self.get_logger().info('Waiting for EKF odom parameter service...')
        if self.ekf_odom_param_client.wait_for_service(timeout_sec=2.0):
            self.get_logger().info('EKF odom parameter service is available')
        else:
            self.get_logger().warning('EKF odom parameter service is NOT available, TF control functionality will be limited')

        # 检查EKF map参数服务是否可用
        self.get_logger().info('Waiting for EKF map parameter service...')
        if self.ekf_map_param_client.wait_for_service(timeout_sec=2.0):
            self.get_logger().info('EKF map parameter service is available')
        else:
            self.get_logger().warning('EKF map parameter service is NOT available, TF control functionality will be limited')

        # 检查NavSat Transform参数服务是否可用
        self.get_logger().info('Waiting for NavSat Transform parameter service...')
        if self.navsat_param_client.wait_for_service(timeout_sec=2.0):
            self.get_logger().info('NavSat Transform parameter service is available')
        else:
            self.get_logger().warning('NavSat Transform parameter service is NOT available, TF control functionality will be limited')

        # 检查室内EKF参数服务是否可用
        self.get_logger().info('Waiting for Indoor EKF parameter service...')
        if self.ekf_indoor_param_client.wait_for_service(timeout_sec=2.0):
            self.get_logger().info('Indoor EKF parameter service is available')
        else:
            self.get_logger().warning('Indoor EKF parameter service is NOT available, indoor TF control functionality will be limited')

        return True

    def load_indoor_areas_config(self):
        """加载室内区域配置文件。"""
        if not self.config_path:
            self.get_logger().warning('No indoor areas config file specified')
            return []

        try:
            if not os.path.exists(self.config_path):
                self.get_logger().error(f'Indoor areas config file not found: {self.config_path}')
                return []

            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            self.get_logger().info(f'Loaded indoor areas config from {self.config_path}')
            return config.get('indoor_areas', [])
        except Exception as e:
            self.get_logger().error(f'Failed to load indoor areas config: {e}')
            return []

    def environment_info_callback(self, msg):
        """处理环境信息回调。"""
        # 保存上一个环境状态，用于检测状态转换
        self.previous_environment = self.current_environment

        # 更新当前环境状态
        self.current_environment = msg.environment
        self.current_area_id = msg.area_id
        self.current_entrance_id = msg.entrance_id

        # 检查是否发生了环境切换
        is_to_indoor = (self.previous_environment != 'indoor' and
                        self.current_environment == 'indoor')
        is_to_outdoor = (self.previous_environment != 'outdoor' and
                         self.current_environment == 'outdoor')

        # 环境有变化
        if is_to_indoor:
            self.get_logger().info(f'检测到环境切换: {self.previous_environment} -> {self.current_environment}，执行室外到室内切换操作')

            # 准备入口信息
            if self.current_area_id and self.current_entrance_id:
                self.pending_entrance_info = self.find_entrance_info(self.current_area_id, self.current_entrance_id)
            else:
                self.pending_entrance_info = None
                self.get_logger().warning('室内环境但缺少有效的区域ID或入口ID')

            # 停用robot_localization包装器
            self.get_logger().info('室外到室内切换：停用robot_localization包装器')
            self.deactivate_robot_localization_wrapper()

            # 激活AMCL节点
            self.get_logger().info('室外到室内切换：激活AMCL节点')
            self.activate_amcl()

            # 激活室内EKF包装器
            self.get_logger().info('室外到室内切换：激活室内EKF包装器')
            self.activate_indoor_ekf_wrapper()

            # 加载室内地图
            if self.pending_entrance_info and 'map_file' in self.pending_entrance_info:
                map_file = self.pending_entrance_info['map_file']
                if map_file != self.current_map_file:
                    self.get_logger().info(f'加载室内地图: {map_file}')
                    if not self.load_map(map_file):
                        self.get_logger().error('室内地图加载失败')
            else:
                self.get_logger().warning('缺少有效的室内地图信息')

        elif is_to_outdoor:
            self.get_logger().info(f'检测到环境切换: {self.previous_environment} -> {self.current_environment}，执行室内到室外切换操作')

            # 清除入口信息
            self.pending_entrance_info = None

            # 取消激活AMCL节点
            self.get_logger().info('室内到室外切换：取消激活AMCL节点')
            self.deactivate_amcl()

            # 取消激活室内EKF包装器
            self.get_logger().info('室内到室外切换：取消激活室内EKF包装器')
            self.deactivate_indoor_ekf_wrapper()

            # 激活robot_localization包装器
            self.activate_robot_localization_wrapper()

            # 加载空白地图
            if self.current_map_file != self.blank_map_path:
                self.get_logger().info(f'加载空白地图: {self.blank_map_path}')
                if not self.load_map(self.blank_map_path):
                    self.get_logger().error('空白地图加载失败')

    def find_entrance_info(self, area_id, entrance_id):
        """查找指定区域和出入口的信息。"""
        # 遍历所有室内区域
        for area in self.indoor_areas:
            # 检查区域ID是否匹配
            if area.get('id') == area_id:
                self.get_logger().debug(f'找到匹配的区域: {area_id}')

                # 检查区域是否有出入口定义
                if 'entrances' not in area or not area['entrances']:
                    self.get_logger().warning(f'区域 {area_id} 没有定义出入口')
                    return None

                # 遍历该区域的所有出入口
                self.get_logger().debug(f'区域 {area_id} 有 {len(area["entrances"])} 个出入口')
                for entrance in area.get('entrances', []):
                    # 记录当前出入口信息
                    self.get_logger().debug(f'检查出入口: {entrance.get("id", "未知ID")} ({entrance.get("name", "未命名")})')

                    # 检查出入口ID是否匹配
                    if entrance.get('id') == entrance_id:
                        self.get_logger().debug(f'找到匹配的出入口: {entrance_id} ({entrance.get("name", "未命名")})')
                        # 检查出入口是否有地图文件
                        if 'map_file' in entrance:
                            self.get_logger().debug(f'出入口 {entrance_id} 的地图文件: {entrance["map_file"]}')
                        else:
                            self.get_logger().warning(f'出入口 {entrance_id} 没有定义地图文件')

                        # 找到匹配的出入口信息，返回
                        return entrance

        # 如果没有找到匹配的出入口信息，返回None
        self.get_logger().warning(f'未找到区域ID={area_id}，入口ID={entrance_id}的信息')
        return None

    def load_map(self, map_file):
        """加载地图。

        Args:
            map_file: 地图文件路径

        Returns:
            bool: 加载成功返回True，否则返回False
        """
        if not map_file:
            self.get_logger().error('未指定地图文件路径')
            return False

        # 检查地图文件是否存在
        if not os.path.exists(map_file):
            self.get_logger().error(f'地图文件不存在: {map_file}')
            return False

        if not self.load_map_available:
            self.get_logger().warning('加载地图服务不可用，跳过地图加载')
            return False

        # 检查服务是否可用
        if not self.load_map_client.service_is_ready():
            self.get_logger().warning('地图加载服务当前不可用，尝试等待...')
            if not self.load_map_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('等待地图加载服务超时')
                return False
            else:
                self.get_logger().info('地图加载服务现在可用')

        # 创建加载地图的请求
        request = LoadMap.Request()
        request.map_url = map_file

        self.get_logger().info(f"正在加载地图: {map_file}")

        # 更新当前地图文件路径
        self.current_map_file = map_file

        # 异步调用加载地图服务，但不等待结果
        try:
            # 调用服务并获取Future对象
            future = self.load_map_client.call_async(request)

            # 添加回调函数来处理结果，而不是阻塞等待
            future.add_done_callback(lambda f: self.handle_load_map_result(f, map_file))

            self.get_logger().info(f'已启动地图 {map_file} 的加载过程')
            return True

        except Exception as e:
            self.get_logger().error(f'启动地图加载过程中发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

            # 即使发生错误，也假设地图已加载或正在加载
            self.get_logger().debug(f'尽管发生错误，仍假定地图 {map_file} 已加载或正在加载')
            return True

    def handle_load_map_result(self, future, map_file):
        """处理地图加载服务的异步结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None:
                    # 打印结果代码和描述
                    result_code = result.result
                    if result_code == LoadMap.Response.RESULT_SUCCESS:
                        self.get_logger().info(f'地图 {map_file} 加载服务调用成功 (结果代码: {result_code})')
                        # 打印地图信息
                        map_info = result.map.info
                        self.get_logger().info(f'加载的地图信息: 宽度={map_info.width}, 高度={map_info.height}, 分辨率={map_info.resolution}')

                    elif result_code == LoadMap.Response.RESULT_MAP_DOES_NOT_EXIST:
                        self.get_logger().error(f'地图 {map_file} 加载失败: 地图文件不存在 (结果代码: {result_code})')
                        # 地图加载失败
                    elif result_code == LoadMap.Response.RESULT_INVALID_MAP_DATA:
                        self.get_logger().error(f'地图 {map_file} 加载失败: 无效的地图数据 (结果代码: {result_code})')
                        # 地图加载失败
                    elif result_code == LoadMap.Response.RESULT_INVALID_MAP_METADATA:
                        self.get_logger().error(f'地图 {map_file} 加载失败: 无效的地图元数据 (结果代码: {result_code})')
                        # 地图加载失败
                    else:
                        self.get_logger().error(f'地图 {map_file} 加载失败: 未定义的错误 (结果代码: {result_code})')
                        # 地图加载失败
                else:
                    self.get_logger().error(f'地图 {map_file} 加载服务调用返回空结果')
                    # 地图加载失败
            else:
                self.get_logger().warning(f'地图 {map_file} 加载服务的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理地图 {map_file} 加载结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            # 发生异常时，地图加载失败

    def activate_robot_localization_wrapper(self):
        """激活robot_localization生命周期包装器"""
        # 直接通过生命周期服务激活robot_localization包装器节点
        try:
            from lifecycle_msgs.srv import GetState

            # 首先检查robot_localization包装器当前状态
            get_state_client = self.create_client(GetState, 'robot_localization_lifecycle_wrapper/get_state')
            if get_state_client.wait_for_service(timeout_sec=3.0):
                get_state_request = GetState.Request()
                get_state_future = get_state_client.call_async(get_state_request)

                # 使用回调处理状态查询结果
                get_state_future.add_done_callback(
                    lambda f: self._handle_get_state_for_activation(f)
                )

                self.get_logger().info('已发送robot_localization包装器状态查询请求（用于激活）')
                return True
            else:
                self.get_logger().error('robot_localization包装器状态查询服务不可用')
                return False

        except Exception as e:
            self.get_logger().error(f'激活robot_localization包装器时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            return False

    def _handle_get_state_for_activation(self, future):
        """处理激活操作的状态查询结果回调"""
        try:
            if future.done():
                state_result = future.result()
                current_state = state_result.current_state.label
                self.get_logger().info(f'robot_localization包装器当前状态: {current_state}（用于激活检查）')

                # 如果已经激活，无需操作
                if current_state == 'active':
                    self.get_logger().info('robot_localization包装器已经处于激活状态，无需重复激活')
                    # 更新TF发布状态跟踪
                    self.ekf_odom_tf_enabled = True
                    self.ekf_map_tf_enabled = True
                    self.navsat_tf_enabled = True
                    return

                # 根据当前状态决定需要的转换序列
                if current_state == 'unconfigured':
                    # 需要先配置，再激活
                    self.get_logger().info('包装器处于unconfigured状态，先执行configure转换')
                    self._send_configure_request()
                elif current_state == 'inactive':
                    # 可以直接激活
                    self.get_logger().info('包装器处于inactive状态，直接执行activate转换')
                    self._send_activation_request()
                else:
                    self.get_logger().warning(f'包装器处于未预期状态: {current_state}，跳过激活操作')
            else:
                self.get_logger().warning('robot_localization包装器状态查询未完成')
        except Exception as e:
            self.get_logger().error(f'处理robot_localization包装器状态查询结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_configure_request(self):
        """发送配置请求"""
        try:
            from lifecycle_msgs.srv import ChangeState
            from lifecycle_msgs.msg import Transition

            # 在configure之前，先设置包装器的TF发布参数
            self._set_wrapper_tf_parameter()

            # 创建直接的生命周期状态变更客户端
            wrapper_lifecycle_client = self.create_client(
                ChangeState,
                'robot_localization_lifecycle_wrapper/change_state'
            )

            if not wrapper_lifecycle_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('robot_localization包装器生命周期服务不可用')
                return

            # 创建配置请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_CONFIGURE  # 1

            # 异步调用服务
            future = wrapper_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self._handle_configure_result(f))

            self.get_logger().info('已发送robot_localization包装器配置请求（直接生命周期控制）')

        except Exception as e:
            self.get_logger().error(f'发送robot_localization包装器配置请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _set_wrapper_tf_parameter(self):
        """设置robot_localization包装器的TF发布参数"""
        try:
            from rcl_interfaces.msg import Parameter, ParameterValue, ParameterType
            from rcl_interfaces.srv import SetParameters

            # 创建包装器参数客户端
            wrapper_param_client = self.create_client(
                SetParameters,
                'robot_localization_lifecycle_wrapper/set_parameters'
            )

            if not wrapper_param_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('robot_localization包装器参数服务不可用')
                return

            # 根据当前环境决定是否启用TF发布
            enable_tf = self.current_environment != 'indoor'

            # 创建参数
            parameter = Parameter()
            parameter.name = 'enable_tf_publishing'
            parameter.value = ParameterValue()
            parameter.value.type = ParameterType.PARAMETER_BOOL
            parameter.value.bool_value = enable_tf

            # 创建请求
            request = SetParameters.Request()
            request.parameters = [parameter]

            self.get_logger().info(f'设置robot_localization包装器TF发布参数为: {enable_tf}（环境: {self.current_environment}）')

            # 同步调用服务
            future = wrapper_param_client.call_async(request)
            # 等待结果
            import time
            timeout = 3.0
            start_time = time.time()
            while not future.done() and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if future.done():
                result = future.result()
                if result and len(result.results) > 0 and result.results[0].successful:
                    self.get_logger().info('robot_localization包装器TF发布参数设置成功')
                else:
                    self.get_logger().error('robot_localization包装器TF发布参数设置失败')
            else:
                self.get_logger().error('robot_localization包装器TF发布参数设置超时')

        except Exception as e:
            self.get_logger().error(f'设置robot_localization包装器TF发布参数时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_configure_result(self, future):
        """处理配置请求的结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('robot_localization包装器配置成功，现在发送激活请求')
                    # 配置成功后，发送激活请求
                    self._send_activation_request()
                else:
                    self.get_logger().error('robot_localization包装器配置失败')
            else:
                self.get_logger().warning('robot_localization包装器配置操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理robot_localization包装器配置结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_activation_request(self):
        """发送激活请求"""
        try:
            from lifecycle_msgs.srv import ChangeState
            from lifecycle_msgs.msg import Transition

            # 创建直接的生命周期状态变更客户端
            wrapper_lifecycle_client = self.create_client(
                ChangeState,
                'robot_localization_lifecycle_wrapper/change_state'
            )

            if not wrapper_lifecycle_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('robot_localization包装器生命周期服务不可用')
                return

            # 创建激活请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_ACTIVATE  # 3

            # 异步调用服务
            future = wrapper_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self.handle_wrapper_lifecycle_result(f, True))

            self.get_logger().info('已发送robot_localization包装器激活请求（直接生命周期控制）')

        except Exception as e:
            self.get_logger().error(f'发送robot_localization包装器激活请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def deactivate_robot_localization_wrapper(self):
        """停用robot_localization生命周期包装器"""
        # 直接通过生命周期服务停用robot_localization包装器节点
        # 而不是使用lifecycle_manager的PAUSE命令（会停用所有节点）
        try:
            from lifecycle_msgs.srv import GetState

            # 首先检查robot_localization包装器当前状态
            get_state_client = self.create_client(GetState, 'robot_localization_lifecycle_wrapper/get_state')
            if get_state_client.wait_for_service(timeout_sec=3.0):
                get_state_request = GetState.Request()
                get_state_future = get_state_client.call_async(get_state_request)

                # 使用回调处理状态查询结果
                get_state_future.add_done_callback(
                    lambda f: self._handle_get_state_for_deactivation(f)
                )

                self.get_logger().info('已发送robot_localization包装器状态查询请求（用于停用）')
                return True
            else:
                self.get_logger().error('robot_localization包装器状态查询服务不可用')
                return False

        except Exception as e:
            self.get_logger().error(f'停用robot_localization包装器时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            return False

    def _handle_get_state_for_deactivation(self, future):
        """处理停用操作的状态查询结果回调"""
        try:
            if future.done():
                state_result = future.result()
                current_state = state_result.current_state.label
                self.get_logger().info(f'robot_localization包装器当前状态: {current_state}（用于停用检查）')

                # 如果已经停用，无需操作
                if current_state in ['inactive', 'unconfigured', 'finalized']:
                    self.get_logger().info(f'robot_localization包装器已经处于非激活状态: {current_state}，无需重复停用')
                    # 更新TF发布状态跟踪
                    self.ekf_odom_tf_enabled = False
                    self.ekf_map_tf_enabled = False
                    self.navsat_tf_enabled = False
                    return

                # 只有在确认包装器是激活状态时才发送停用请求
                self._send_deactivation_request()
            else:
                self.get_logger().warning('robot_localization包装器状态查询未完成')
        except Exception as e:
            self.get_logger().error(f'处理robot_localization包装器状态查询结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_deactivation_request(self):
        """发送停用请求"""
        try:
            from lifecycle_msgs.srv import ChangeState
            from lifecycle_msgs.msg import Transition

            # 创建直接的生命周期状态变更客户端
            wrapper_lifecycle_client = self.create_client(
                ChangeState,
                'robot_localization_lifecycle_wrapper/change_state'
            )

            if not wrapper_lifecycle_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('robot_localization包装器生命周期服务不可用')
                return

            # 创建停用请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_DEACTIVATE  # 4

            # 异步调用服务
            future = wrapper_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self.handle_wrapper_lifecycle_result(f, False))

            self.get_logger().info('已发送robot_localization包装器停用请求（直接生命周期控制）')

        except Exception as e:
            self.get_logger().error(f'发送robot_localization包装器停用请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def handle_wrapper_lifecycle_result(self, future, is_activation):
        """处理robot_localization包装器直接生命周期控制的异步结果"""
        try:
            if future.done():
                result = future.result()
                action = "激活" if is_activation else "停用"

                if result is not None and result.success:
                    self.get_logger().info(f'robot_localization包装器{action}成功（直接生命周期控制）')

                    # 更新robot_localization节点的TF发布状态跟踪
                    if is_activation:
                        # 激活成功，TF发布参数应该已经在configure阶段设置好了
                        self.get_logger().info('robot_localization包装器激活成功，开始监控TF变换')
                        self.ekf_odom_tf_enabled = True
                        self.ekf_map_tf_enabled = True
                        self.navsat_tf_enabled = True
                        # 开始监控TF变换
                        self._start_tf_monitoring_for_navigation()
                    else:
                        # 停用时，假设所有robot_localization节点的TF发布都禁用
                        self.ekf_odom_tf_enabled = False
                        self.ekf_map_tf_enabled = False
                        self.navsat_tf_enabled = False
                else:
                    # 提供更详细的错误信息
                    if result is None:
                        self.get_logger().error(f'robot_localization包装器{action}失败：服务调用返回空结果（直接生命周期控制）')
                    else:
                        self.get_logger().error(f'robot_localization包装器{action}失败：success={result.success}（直接生命周期控制）')
                        # 可能的原因：包装器已经处于目标状态，或者状态转换不被允许
                        if is_activation:
                            self.get_logger().info('提示：包装器可能已经处于激活状态，或者当前状态不允许激活转换')
                        else:
                            self.get_logger().info('提示：包装器可能已经处于非激活状态，或者当前状态不允许停用转换')
            else:
                self.get_logger().warning('robot_localization包装器直接生命周期操作的Future尚未完成')
        except Exception as e:
            action = "激活" if is_activation else "停用"
            self.get_logger().error(f'处理robot_localization包装器{action}结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())


    def _start_tf_monitoring_for_navigation(self):
        """开始监控TF变换，准备启动导航系统"""
        try:
            # 标记导航系统启动待处理
            self.navigation_startup_pending = True

            # 取消之前的TF检查定时器（如果存在）
            if self.tf_check_timer is not None:
                self.tf_check_timer.cancel()

            # 重置检查计数器
            self._tf_check_count = 0
            self._behavior_check_count = 0
            self._navigation_startup_retry_count = 0

            # 创建TF检查定时器，每0.5秒检查一次
            self.tf_check_timer = self.create_timer(0.5, self._check_tf_and_start_navigation)

            self.get_logger().info('开始监控TF变换，准备启动导航系统')

            # 同时启动一个延迟的导航系统强制启动定时器（作为备用方案）
            # 如果30秒后还没有启动成功，强制启动
            def force_startup_once():
                self._force_navigation_startup()
                # 取消定时器，确保只执行一次
                if hasattr(self, '_force_startup_timer') and self._force_startup_timer is not None:
                    self._force_startup_timer.cancel()
                    self._force_startup_timer = None

            self._force_startup_timer = self.create_timer(30.0, force_startup_once)

        except Exception as e:
            self.get_logger().error(f'启动TF监控时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _check_tf_and_start_navigation(self):
        """检查TF变换是否可用，如果可用则启动导航系统"""
        try:
            # 检查关键的TF变换是否可用
            # 先检查基础变换，然后检查复合变换
            required_transforms = [
                ('odom', 'base_link'),  # 最基础的变换，通常由robot_state_publisher或wheel odometry提供
                ('map', 'odom'),       # 由EKF map节点提供
            ]

            available_transforms = []
            missing_transforms = []

            for parent_frame, child_frame in required_transforms:
                try:
                    # 检查变换是否可用（不获取实际变换，只检查可用性）
                    if self.tf_buffer.can_transform(
                        parent_frame, child_frame,
                        rclpy.time.Time(),
                        timeout=rclpy.duration.Duration(seconds=0.1)
                    ):
                        available_transforms.append(f'{parent_frame} -> {child_frame}')
                        self.get_logger().debug(f'TF变换 {parent_frame} -> {child_frame} 可用')
                    else:
                        missing_transforms.append(f'{parent_frame} -> {child_frame}')
                        self.get_logger().debug(f'TF变换 {parent_frame} -> {child_frame} 尚未可用')
                except Exception as e:
                    missing_transforms.append(f'{parent_frame} -> {child_frame}')
                    self.get_logger().debug(f'检查TF变换 {parent_frame} -> {child_frame} 时发生错误: {e}')

            # 记录当前状态
            self.get_logger().debug(f'可用的TF变换: {available_transforms}')
            if missing_transforms:
                self.get_logger().debug(f'缺失的TF变换: {missing_transforms}')

            # 如果所有基础变换都可用，检查behavior_server状态
            if len(missing_transforms) == 0:
                self.get_logger().info('所有必需的TF变换已可用，检查behavior_server状态...')

                # 检查behavior_server的wait动作服务器是否可用
                if self._check_behavior_server_ready():
                    self.get_logger().info('behavior_server已准备就绪，启动导航系统')
                    self._start_navigation_system_successfully()
                else:
                    # behavior_server尚未准备好，继续等待
                    if not hasattr(self, '_behavior_check_count'):
                        self._behavior_check_count = 0
                    self._behavior_check_count += 1

                    if self._behavior_check_count % 10 == 0:
                        self.get_logger().info(f'等待behavior_server准备就绪... 已检查{self._behavior_check_count}次')

                    # 如果等待时间过长，尝试强制启动behavior_server
                    if self._behavior_check_count >= 20:  # 10秒后
                        self.get_logger().warning('behavior_server等待时间过长，尝试强制启动')
                        self._force_behavior_server_startup()
            else:
                # 每10次检查输出一次信息级别的日志
                if not hasattr(self, '_tf_check_count'):
                    self._tf_check_count = 0
                self._tf_check_count += 1

                if self._tf_check_count % 10 == 0:
                    self.get_logger().info(f'等待TF变换可用... 已检查{self._tf_check_count}次，缺失: {missing_transforms}')

        except Exception as e:
            self.get_logger().error(f'检查TF变换时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _check_behavior_server_ready(self):
        """检查behavior_server是否准备就绪"""
        try:
            # 创建wait动作客户端来检查服务是否可用
            import rclpy.action
            from nav2_msgs.action import Wait

            # 如果还没有创建动作客户端，创建一个
            if not hasattr(self, '_wait_action_client'):
                self._wait_action_client = rclpy.action.ActionClient(self, Wait, 'wait')
                self.get_logger().debug('创建了wait动作客户端')

            # 检查动作服务器是否可用（等待一小段时间）
            if self._wait_action_client.wait_for_server(timeout_sec=0.1):
                self.get_logger().debug('wait动作服务器已准备就绪')
                return True
            else:
                self.get_logger().debug('wait动作服务器尚未准备就绪')
                return False

        except Exception as e:
            self.get_logger().debug(f'检查behavior_server状态时发生错误: {e}')
            return False

    def _start_navigation_system_successfully(self):
        """成功启动导航系统（清理定时器并启动）"""
        try:
            # 取消TF检查定时器
            if self.tf_check_timer is not None:
                self.tf_check_timer.cancel()
                self.tf_check_timer = None

            # 取消强制启动定时器
            if hasattr(self, '_force_startup_timer') and self._force_startup_timer is not None:
                self._force_startup_timer.cancel()
                self._force_startup_timer = None

            # 重置待处理标志
            self.navigation_startup_pending = False

            # 启动导航系统
            self._startup_navigation_system()

        except Exception as e:
            self.get_logger().error(f'成功启动导航系统时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _force_behavior_server_startup(self):
        """强制启动behavior_server"""
        try:
            self.get_logger().info('强制启动behavior_server...')

            # 尝试直接配置和激活behavior_server
            from lifecycle_msgs.srv import ChangeState
            from lifecycle_msgs.msg import Transition

            # 创建behavior_server生命周期客户端
            behavior_lifecycle_client = self.create_client(
                ChangeState, 'behavior_server/change_state')

            if behavior_lifecycle_client.wait_for_service(timeout_sec=3.0):
                # 先尝试配置
                configure_request = ChangeState.Request()
                configure_request.transition.id = Transition.TRANSITION_CONFIGURE

                configure_future = behavior_lifecycle_client.call_async(configure_request)
                configure_future.add_done_callback(
                    lambda f: self._handle_force_behavior_configure_result(f, behavior_lifecycle_client)
                )

                self.get_logger().info('已发送behavior_server强制配置请求')
            else:
                self.get_logger().error('behavior_server生命周期服务不可用')
                # 如果强制启动失败，尝试强制启动整个导航系统
                self._force_navigation_startup()

        except Exception as e:
            self.get_logger().error(f'强制启动behavior_server时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            # 如果强制启动失败，尝试强制启动整个导航系统
            self._force_navigation_startup()

    def _handle_force_behavior_configure_result(self, future, behavior_lifecycle_client):
        """处理强制behavior_server配置结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('behavior_server强制配置成功，现在激活')
                    # 配置成功，现在激活
                    from lifecycle_msgs.srv import ChangeState
                    from lifecycle_msgs.msg import Transition

                    activate_request = ChangeState.Request()
                    activate_request.transition.id = Transition.TRANSITION_ACTIVATE

                    activate_future = behavior_lifecycle_client.call_async(activate_request)
                    activate_future.add_done_callback(self._handle_force_behavior_activate_result)
                else:
                    self.get_logger().warning('behavior_server强制配置失败，尝试强制启动整个导航系统')
                    self._force_navigation_startup()
            else:
                self.get_logger().warning('behavior_server强制配置操作未完成')
                self._force_navigation_startup()
        except Exception as e:
            self.get_logger().error(f'处理behavior_server强制配置结果时发生错误: {e}')
            self._force_navigation_startup()

    def _handle_force_behavior_activate_result(self, future):
        """处理强制behavior_server激活结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('behavior_server强制激活成功，现在启动导航系统')
                    # 等待一小段时间让behavior_server完全准备好
                    def delayed_startup():
                        self._start_navigation_system_successfully()
                        # 取消定时器，确保只执行一次
                        if hasattr(self, '_delayed_startup_timer') and self._delayed_startup_timer is not None:
                            self._delayed_startup_timer.cancel()
                            self._delayed_startup_timer = None

                    self._delayed_startup_timer = self.create_timer(2.0, delayed_startup)
                else:
                    self.get_logger().warning('behavior_server强制激活失败，尝试强制启动整个导航系统')
                    self._force_navigation_startup()
            else:
                self.get_logger().warning('behavior_server强制激活操作未完成')
                self._force_navigation_startup()
        except Exception as e:
            self.get_logger().error(f'处理behavior_server强制激活结果时发生错误: {e}')
            self._force_navigation_startup()

    def _force_navigation_startup(self):
        """强制启动导航系统（备用方案）"""
        try:
            # 取消强制启动定时器
            if hasattr(self, '_force_startup_timer') and self._force_startup_timer is not None:
                self._force_startup_timer.cancel()
                self._force_startup_timer = None

            self.get_logger().warning('强制启动导航系统（备用方案）')

            # 直接启动导航系统，不等待behavior_server
            self._start_navigation_system_successfully()

        except Exception as e:
            self.get_logger().error(f'强制启动导航系统时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _startup_navigation_system(self):
        """启动导航系统"""
        try:
            if not self.nav_lifecycle_available:
                self.get_logger().warning('导航生命周期管理器服务不可用，无法启动导航系统')
                return

            # 检查导航生命周期管理器服务是否可用
            if not self.nav_lifecycle_client.service_is_ready():
                self.get_logger().warning('导航生命周期管理器服务当前不可用，尝试等待...')
                if not self.nav_lifecycle_client.wait_for_service(timeout_sec=3.0):
                    self.get_logger().error('等待导航生命周期管理器服务超时')
                    return
                else:
                    self.get_logger().info('导航生命周期管理器服务现在可用')

            # 检查所有导航节点的状态，决定使用哪种启动策略
            self._check_navigation_nodes_status_and_start()

        except Exception as e:
            self.get_logger().error(f'启动导航系统时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _check_navigation_nodes_status_and_start(self):
        """检查导航节点状态并选择合适的启动策略"""
        try:
            # 导航节点列表
            nav_nodes = ['controller_server', 'planner_server', 'behavior_server', 'bt_navigator']

            # 检查所有节点状态
            self._nav_node_states = {}
            self._nav_nodes_to_check = nav_nodes.copy()

            for node_name in nav_nodes:
                self._check_single_nav_node_status(node_name)

        except Exception as e:
            self.get_logger().error(f'检查导航节点状态时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _check_single_nav_node_status(self, node_name):
        """检查单个导航节点的状态"""
        try:
            from lifecycle_msgs.srv import GetState

            # 创建状态查询客户端
            get_state_client = self.create_client(GetState, f'{node_name}/get_state')

            if get_state_client.wait_for_service(timeout_sec=1.0):
                get_state_request = GetState.Request()
                get_state_future = get_state_client.call_async(get_state_request)

                # 使用回调处理状态查询结果
                get_state_future.add_done_callback(
                    lambda f, name=node_name: self._handle_nav_node_status_result(f, name)
                )

                self.get_logger().debug(f'已发送{node_name}状态查询请求')
            else:
                self.get_logger().warning(f'{node_name}状态查询服务不可用，假设为unconfigured')
                self._nav_node_states[node_name] = 'unconfigured'
                self._nav_nodes_to_check.remove(node_name)
                self._check_if_all_nav_nodes_checked()

        except Exception as e:
            self.get_logger().error(f'检查{node_name}状态时发生错误: {e}')
            # 假设节点处于unconfigured状态
            self._nav_node_states[node_name] = 'unconfigured'
            if node_name in self._nav_nodes_to_check:
                self._nav_nodes_to_check.remove(node_name)
            self._check_if_all_nav_nodes_checked()

    def _handle_nav_node_status_result(self, future, node_name):
        """处理导航节点状态查询结果"""
        try:
            if future.done():
                result = future.result()
                current_state = result.current_state.label
                self._nav_node_states[node_name] = current_state
                self.get_logger().debug(f'{node_name}当前状态: {current_state}')
            else:
                self.get_logger().warning(f'{node_name}状态查询未完成，假设为unconfigured')
                self._nav_node_states[node_name] = 'unconfigured'

            # 从待检查列表中移除
            if node_name in self._nav_nodes_to_check:
                self._nav_nodes_to_check.remove(node_name)

            # 检查是否所有节点都已检查完毕
            self._check_if_all_nav_nodes_checked()

        except Exception as e:
            self.get_logger().error(f'处理{node_name}状态查询结果时发生错误: {e}')
            self._nav_node_states[node_name] = 'unconfigured'
            if node_name in self._nav_nodes_to_check:
                self._nav_nodes_to_check.remove(node_name)
            self._check_if_all_nav_nodes_checked()

    def _check_if_all_nav_nodes_checked(self):
        """检查是否所有导航节点状态都已检查完毕"""
        if len(self._nav_nodes_to_check) == 0:
            # 所有节点状态都已获取，决定启动策略
            self._decide_navigation_startup_strategy()

    def _decide_navigation_startup_strategy(self):
        """根据节点状态决定启动策略"""
        try:
            self.get_logger().info(f'导航节点状态: {self._nav_node_states}')

            # 统计各种状态的节点数量
            unconfigured_nodes = [name for name, state in self._nav_node_states.items() if state == 'unconfigured']
            inactive_nodes = [name for name, state in self._nav_node_states.items() if state == 'inactive']
            active_nodes = [name for name, state in self._nav_node_states.items() if state == 'active']

            self.get_logger().info(f'unconfigured节点: {unconfigured_nodes}')
            self.get_logger().info(f'inactive节点: {inactive_nodes}')
            self.get_logger().info(f'active节点: {active_nodes}')

            # 如果所有节点都是active，导航系统已经启动
            if len(active_nodes) == len(self._nav_node_states):
                self.get_logger().info('所有导航节点都已激活，导航系统已启动')
                return

            # 如果有unconfigured节点，需要先处理它们
            if len(unconfigured_nodes) > 0:
                self.get_logger().info(f'检测到unconfigured节点: {unconfigured_nodes}，需要先配置这些节点')
                # 如果有active节点，使用个别节点控制而不是lifecycle_manager的SHUTDOWN
                if len(active_nodes) > 0:
                    self.get_logger().info('存在active节点，先单独停止active节点再重新启动所有节点')
                    self._deactivate_active_nodes_and_restart(active_nodes)
                else:
                    # 没有active节点，可以直接使用STARTUP
                    self.get_logger().info('没有active节点，使用STARTUP命令启动所有节点')
                    startup_request = ManageLifecycleNodes.Request()
                    startup_request.command = ManageLifecycleNodes.Request.STARTUP

                    startup_future = self.nav_lifecycle_client.call_async(startup_request)
                    startup_future.add_done_callback(self._handle_navigation_startup_result)
            else:
                # 没有unconfigured节点，只有inactive和active节点
                if len(active_nodes) > 0:
                    self.get_logger().info('只有inactive和active节点，使用RESUME命令激活剩余节点')
                    self._send_navigation_resume_request()
                else:
                    # 所有节点都是inactive，可以直接使用RESUME
                    self.get_logger().info('所有节点都是inactive，使用RESUME命令激活')
                    self._send_navigation_resume_request()

        except Exception as e:
            self.get_logger().error(f'决定导航启动策略时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _deactivate_active_nodes_and_restart(self, active_nodes):
        """单独停止active节点然后重新启动所有节点"""
        try:
            self.get_logger().info(f'单独停止active节点: {active_nodes}')

            # 记录需要停止的节点
            self._nodes_to_deactivate = active_nodes.copy()
            self._deactivated_nodes = []

            # 逐个停止active节点
            for node_name in active_nodes:
                self._deactivate_single_node(node_name)

        except Exception as e:
            self.get_logger().error(f'停止active节点时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            # 如果单独停止失败，直接尝试启动
            self._restart_all_navigation_nodes()

    def _deactivate_single_node(self, node_name):
        """停止单个节点"""
        try:
            from lifecycle_msgs.srv import ChangeState
            from lifecycle_msgs.msg import Transition

            # 创建节点生命周期客户端
            deactivate_client = self.create_client(ChangeState, f'{node_name}/change_state')

            if deactivate_client.wait_for_service(timeout_sec=1.0):
                # 发送deactivate请求
                deactivate_request = ChangeState.Request()
                deactivate_request.transition.id = Transition.TRANSITION_DEACTIVATE

                deactivate_future = deactivate_client.call_async(deactivate_request)
                deactivate_future.add_done_callback(
                    lambda f, name=node_name: self._handle_single_node_deactivate_result(f, name)
                )

                self.get_logger().info(f'已发送{node_name}停止请求')
            else:
                self.get_logger().warning(f'{node_name}生命周期服务不可用，跳过停止')
                # 从待停止列表中移除
                if node_name in self._nodes_to_deactivate:
                    self._nodes_to_deactivate.remove(node_name)
                self._check_if_all_nodes_deactivated()

        except Exception as e:
            self.get_logger().error(f'停止{node_name}时发生错误: {e}')
            # 从待停止列表中移除
            if node_name in self._nodes_to_deactivate:
                self._nodes_to_deactivate.remove(node_name)
            self._check_if_all_nodes_deactivated()

    def _handle_single_node_deactivate_result(self, future, node_name):
        """处理单个节点停止结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info(f'{node_name}停止成功')
                    self._deactivated_nodes.append(node_name)
                else:
                    self.get_logger().warning(f'{node_name}停止失败，但继续处理')

                # 从待停止列表中移除
                if node_name in self._nodes_to_deactivate:
                    self._nodes_to_deactivate.remove(node_name)

                # 检查是否所有节点都已处理
                self._check_if_all_nodes_deactivated()
            else:
                self.get_logger().warning(f'{node_name}停止操作未完成')

        except Exception as e:
            self.get_logger().error(f'处理{node_name}停止结果时发生错误: {e}')
            # 从待停止列表中移除
            if node_name in self._nodes_to_deactivate:
                self._nodes_to_deactivate.remove(node_name)
            self._check_if_all_nodes_deactivated()

    def _check_if_all_nodes_deactivated(self):
        """检查是否所有节点都已停止"""
        if len(self._nodes_to_deactivate) == 0:
            self.get_logger().info(f'所有active节点已处理，成功停止: {self._deactivated_nodes}')
            # 等待一小段时间确保节点完全停止
            def delayed_restart():
                self._restart_all_navigation_nodes()
                # 取消定时器
                if hasattr(self, '_deactivate_delay_timer') and self._deactivate_delay_timer is not None:
                    self._deactivate_delay_timer.cancel()
                    self._deactivate_delay_timer = None

            self._deactivate_delay_timer = self.create_timer(1.0, delayed_restart)

    def _restart_all_navigation_nodes(self):
        """重新启动所有导航节点"""
        try:
            self.get_logger().info('重新启动所有导航节点...')

            startup_request = ManageLifecycleNodes.Request()
            startup_request.command = ManageLifecycleNodes.Request.STARTUP

            startup_future = self.nav_lifecycle_client.call_async(startup_request)
            startup_future.add_done_callback(self._handle_navigation_startup_result)

        except Exception as e:
            self.get_logger().error(f'重新启动导航节点时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _shutdown_and_restart_navigation(self):
        """停止所有导航节点然后重新启动"""
        try:
            self.get_logger().info('停止所有导航节点...')

            # 发送SHUTDOWN命令停止所有节点
            shutdown_request = ManageLifecycleNodes.Request()
            shutdown_request.command = ManageLifecycleNodes.Request.SHUTDOWN

            shutdown_future = self.nav_lifecycle_client.call_async(shutdown_request)
            shutdown_future.add_done_callback(self._handle_navigation_shutdown_result)

        except Exception as e:
            self.get_logger().error(f'停止导航节点时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_navigation_shutdown_result(self, future):
        """处理导航系统停止结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('导航系统停止成功，现在重新启动')
                    # 等待一小段时间确保节点完全停止
                    def delayed_restart():
                        startup_request = ManageLifecycleNodes.Request()
                        startup_request.command = ManageLifecycleNodes.Request.STARTUP

                        startup_future = self.nav_lifecycle_client.call_async(startup_request)
                        startup_future.add_done_callback(self._handle_navigation_startup_result)

                        # 取消定时器
                        if hasattr(self, '_restart_timer') and self._restart_timer is not None:
                            self._restart_timer.cancel()
                            self._restart_timer = None

                    self._restart_timer = self.create_timer(2.0, delayed_restart)
                else:
                    self.get_logger().error('导航系统停止失败，尝试直接启动')
                    # 即使停止失败，也尝试启动
                    startup_request = ManageLifecycleNodes.Request()
                    startup_request.command = ManageLifecycleNodes.Request.STARTUP

                    startup_future = self.nav_lifecycle_client.call_async(startup_request)
                    startup_future.add_done_callback(self._handle_navigation_startup_result)
            else:
                self.get_logger().warning('导航系统停止操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理导航系统停止结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_navigation_startup_result(self, future):
        """处理导航系统启动结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('导航系统STARTUP成功，现在发送RESUME请求（激活节点）')
                    # STARTUP成功后，发送RESUME命令激活所有节点
                    self._send_navigation_resume_request()
                else:
                    self.get_logger().error('导航系统STARTUP失败')
                    # 增加重试机制
                    if not hasattr(self, '_navigation_startup_retry_count'):
                        self._navigation_startup_retry_count = 0

                    self._navigation_startup_retry_count += 1
                    max_retries = 3

                    if self._navigation_startup_retry_count < max_retries:
                        self.get_logger().info(f'导航系统启动失败，将在5秒后重试 (第{self._navigation_startup_retry_count}次/共{max_retries}次)')
                        # 延迟重试
                        # 先取消之前的重试定时器（如果存在）
                        if hasattr(self, '_navigation_retry_timer') and self._navigation_retry_timer is not None:
                            self._navigation_retry_timer.cancel()

                        retry_timer = self.create_timer(5.0, self._retry_navigation_startup)
                        self._navigation_retry_timer = retry_timer
                    else:
                        self.get_logger().error(f'导航系统启动失败，已达到最大重试次数({max_retries})')
            else:
                self.get_logger().warning('导航系统启动操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理导航系统启动结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_navigation_resume_request(self):
        """发送导航系统RESUME请求"""
        try:
            # 创建RESUME请求（激活所有节点）
            resume_request = ManageLifecycleNodes.Request()
            resume_request.command = ManageLifecycleNodes.Request.RESUME

            self.get_logger().info('发送导航系统RESUME请求（激活节点）')

            # 异步调用RESUME服务
            resume_future = self.nav_lifecycle_client.call_async(resume_request)
            resume_future.add_done_callback(self._handle_navigation_resume_result)

        except Exception as e:
            self.get_logger().error(f'发送导航系统RESUME请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_navigation_resume_result(self, future):
        """处理导航系统RESUME结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('导航系统RESUME成功，导航系统已完全启动')
                else:
                    self.get_logger().error('导航系统RESUME失败')
            else:
                self.get_logger().warning('导航系统RESUME操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理导航系统RESUME结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _retry_navigation_startup(self):
        """重试导航系统启动"""
        try:
            # 销毁重试定时器
            if hasattr(self, '_navigation_retry_timer') and self._navigation_retry_timer is not None:
                self._navigation_retry_timer.cancel()
                self._navigation_retry_timer = None

            self.get_logger().info('重试启动导航系统...')

            # 在重试之前，先尝试强制启动behavior_server
            if hasattr(self, '_navigation_startup_retry_count') and self._navigation_startup_retry_count >= 2:
                self.get_logger().info('多次重试失败，尝试强制启动behavior_server')
                self._force_behavior_server_startup()
            else:
                self._startup_navigation_system()

        except Exception as e:
            self.get_logger().error(f'重试导航系统启动时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def deactivate_amcl(self):
        """取消激活AMCL节点"""
        try:
            # 检查AMCL生命周期服务是否可用
            if not self.amcl_lifecycle_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('AMCL生命周期服务不可用')
                return False

            # 创建取消激活请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_DEACTIVATE  # 4

            self.get_logger().info('发送AMCL取消激活请求')

            # 异步调用服务
            future = self.amcl_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self._handle_amcl_deactivate_result(f))

            return True

        except Exception as e:
            self.get_logger().error(f'取消激活AMCL时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            return False

    def _handle_amcl_deactivate_result(self, future):
        """处理AMCL取消激活结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('AMCL节点取消激活成功')
                    # 更新AMCL TF发布状态跟踪
                    self.amcl_tf_enabled = False
                else:
                    self.get_logger().error('AMCL节点取消激活失败')
            else:
                self.get_logger().warning('AMCL取消激活操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理AMCL取消激活结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def deactivate_indoor_ekf_wrapper(self):
        """取消激活室内EKF包装器"""
        try:
            # 检查室内EKF包装器生命周期服务是否可用
            if not self.indoor_ekf_lifecycle_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('室内EKF包装器生命周期服务不可用')
                return False

            # 创建取消激活请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_DEACTIVATE  # 4

            self.get_logger().info('发送室内EKF包装器取消激活请求')

            # 异步调用服务
            future = self.indoor_ekf_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self._handle_indoor_ekf_deactivate_result(f))

            return True

        except Exception as e:
            self.get_logger().error(f'取消激活室内EKF包装器时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            return False

    def _handle_indoor_ekf_deactivate_result(self, future):
        """处理室内EKF包装器取消激活结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('室内EKF包装器取消激活成功')
                    # 更新室内EKF TF发布状态跟踪
                    self.ekf_indoor_tf_enabled = False
                else:
                    self.get_logger().error('室内EKF包装器取消激活失败')
            else:
                self.get_logger().warning('室内EKF包装器取消激活操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理室内EKF包装器取消激活结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def activate_amcl(self):
        """激活AMCL节点"""
        try:
            # 检查AMCL生命周期服务是否可用
            if not self.amcl_lifecycle_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('AMCL生命周期服务不可用')
                return False

            # 首先检查AMCL当前状态
            get_state_client = self.create_client(GetState, 'amcl/get_state')
            if get_state_client.wait_for_service(timeout_sec=3.0):
                get_state_request = GetState.Request()
                get_state_future = get_state_client.call_async(get_state_request)

                # 使用回调处理状态查询结果
                get_state_future.add_done_callback(
                    lambda f: self._handle_amcl_get_state_for_activation(f)
                )

                self.get_logger().info('已发送AMCL状态查询请求（用于激活）')
                return True
            else:
                self.get_logger().error('AMCL状态查询服务不可用')
                return False

        except Exception as e:
            self.get_logger().error(f'激活AMCL时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            return False

    def _handle_amcl_get_state_for_activation(self, future):
        """处理AMCL激活操作的状态查询结果回调"""
        try:
            if future.done():
                state_result = future.result()
                current_state = state_result.current_state.label
                self.get_logger().info(f'AMCL当前状态: {current_state}（用于激活检查）')

                # 如果已经激活，无需操作
                if current_state == 'active':
                    self.get_logger().info('AMCL已经处于激活状态，无需重复激活')
                    # 更新TF发布状态跟踪
                    self.amcl_tf_enabled = True
                    return

                # 根据当前状态决定需要的转换序列
                if current_state == 'unconfigured':
                    # 需要先配置，再激活
                    self.get_logger().info('AMCL处于unconfigured状态，先执行configure转换')
                    self._send_amcl_configure_request()
                elif current_state == 'inactive':
                    # 可以直接激活
                    self.get_logger().info('AMCL处于inactive状态，直接执行activate转换')
                    self._send_amcl_activation_request()
                else:
                    self.get_logger().warning(f'AMCL处于未预期状态: {current_state}，跳过激活操作')
            else:
                self.get_logger().warning('AMCL状态查询未完成')
        except Exception as e:
            self.get_logger().error(f'处理AMCL状态查询结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_amcl_configure_request(self):
        """发送AMCL配置请求"""
        try:
            # 创建配置请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_CONFIGURE  # 1

            # 异步调用服务
            future = self.amcl_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self._handle_amcl_configure_result(f))

            self.get_logger().info('已发送AMCL配置请求')

        except Exception as e:
            self.get_logger().error(f'发送AMCL配置请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_amcl_configure_result(self, future):
        """处理AMCL配置请求的结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('AMCL配置成功，现在发送激活请求')
                    # 配置成功后，发送激活请求
                    self._send_amcl_activation_request()
                else:
                    self.get_logger().error('AMCL配置失败')
            else:
                self.get_logger().warning('AMCL配置操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理AMCL配置结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_amcl_activation_request(self):
        """发送AMCL激活请求"""
        try:
            # 创建激活请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_ACTIVATE  # 3

            # 异步调用服务
            future = self.amcl_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self._handle_amcl_activate_result(f))

            self.get_logger().info('已发送AMCL激活请求')

        except Exception as e:
            self.get_logger().error(f'发送AMCL激活请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_amcl_activate_result(self, future):
        """处理AMCL激活结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('AMCL节点激活成功')
                    # 更新AMCL TF发布状态跟踪
                    self.amcl_tf_enabled = True

                    # AMCL激活成功后，设置初始位置
                    self.get_logger().info('AMCL激活成功，现在设置初始位置')
                    # 延迟更长时间确保AMCL服务完全可用
                    timer = self.create_timer(3.0, self._delayed_set_amcl_initial_pose)
                    # 保存定时器引用以便在回调中销毁
                    self._initial_pose_timer = timer
                    # 初始化重试计数器
                    self._initial_pose_retry_count = 0
                else:
                    self.get_logger().error('AMCL节点激活失败')
            else:
                self.get_logger().warning('AMCL激活操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理AMCL激活结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def activate_indoor_ekf_wrapper(self):
        """激活室内EKF包装器"""
        try:
            # 检查室内EKF包装器生命周期服务是否可用
            if not self.indoor_ekf_lifecycle_client.wait_for_service(timeout_sec=3.0):
                self.get_logger().error('室内EKF包装器生命周期服务不可用')
                return False

            # 首先检查室内EKF包装器当前状态
            get_state_client = self.create_client(GetState, 'indoor_ekf_lifecycle_wrapper/get_state')
            if get_state_client.wait_for_service(timeout_sec=3.0):
                get_state_request = GetState.Request()
                get_state_future = get_state_client.call_async(get_state_request)

                # 使用回调处理状态查询结果
                get_state_future.add_done_callback(
                    lambda f: self._handle_indoor_ekf_get_state_for_activation(f)
                )

                self.get_logger().info('已发送室内EKF包装器状态查询请求（用于激活）')
                return True
            else:
                self.get_logger().error('室内EKF包装器状态查询服务不可用')
                return False

        except Exception as e:
            self.get_logger().error(f'激活室内EKF包装器时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            return False

    def _handle_indoor_ekf_get_state_for_activation(self, future):
        """处理室内EKF包装器激活操作的状态查询结果回调"""
        try:
            if future.done():
                state_result = future.result()
                current_state = state_result.current_state.label
                self.get_logger().info(f'室内EKF包装器当前状态: {current_state}（用于激活检查）')

                # 如果已经激活，无需操作
                if current_state == 'active':
                    self.get_logger().info('室内EKF包装器已经处于激活状态，无需重复激活')
                    # 更新TF发布状态跟踪
                    self.ekf_indoor_tf_enabled = True
                    return

                # 根据当前状态决定需要的转换序列
                if current_state == 'unconfigured':
                    # 需要先配置，再激活
                    self.get_logger().info('室内EKF包装器处于unconfigured状态，先执行configure转换')
                    self._send_indoor_ekf_configure_request()
                elif current_state == 'inactive':
                    # 可以直接激活
                    self.get_logger().info('室内EKF包装器处于inactive状态，直接执行activate转换')
                    self._send_indoor_ekf_activation_request()
                else:
                    self.get_logger().warning(f'室内EKF包装器处于未预期状态: {current_state}，跳过激活操作')
            else:
                self.get_logger().warning('室内EKF包装器状态查询未完成')
        except Exception as e:
            self.get_logger().error(f'处理室内EKF包装器状态查询结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_indoor_ekf_configure_request(self):
        """发送室内EKF包装器配置请求"""
        try:
            # 创建配置请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_CONFIGURE  # 1

            # 异步调用服务
            future = self.indoor_ekf_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self._handle_indoor_ekf_configure_result(f))

            self.get_logger().info('已发送室内EKF包装器配置请求')

        except Exception as e:
            self.get_logger().error(f'发送室内EKF包装器配置请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_indoor_ekf_configure_result(self, future):
        """处理室内EKF包装器配置请求的结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('室内EKF包装器配置成功，现在发送激活请求')
                    # 配置成功后，发送激活请求
                    self._send_indoor_ekf_activation_request()
                else:
                    self.get_logger().error('室内EKF包装器配置失败')
            else:
                self.get_logger().warning('室内EKF包装器配置操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理室内EKF包装器配置结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _send_indoor_ekf_activation_request(self):
        """发送室内EKF包装器激活请求"""
        try:
            # 创建激活请求
            request = ChangeState.Request()
            request.transition.id = Transition.TRANSITION_ACTIVATE  # 3

            # 异步调用服务
            future = self.indoor_ekf_lifecycle_client.call_async(request)
            future.add_done_callback(lambda f: self._handle_indoor_ekf_activate_result(f))

            self.get_logger().info('已发送室内EKF包装器激活请求')

        except Exception as e:
            self.get_logger().error(f'发送室内EKF包装器激活请求时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())

    def _handle_indoor_ekf_activate_result(self, future):
        """处理室内EKF包装器激活结果"""
        try:
            if future.done():
                result = future.result()
                if result is not None and result.success:
                    self.get_logger().info('室内EKF包装器激活成功')
                    # 更新室内EKF TF发布状态跟踪
                    self.ekf_indoor_tf_enabled = True
                else:
                    self.get_logger().error('室内EKF包装器激活失败')
            else:
                self.get_logger().warning('室内EKF包装器激活操作的Future尚未完成')
        except Exception as e:
            self.get_logger().error(f'处理室内EKF包装器激活结果时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())


def main(args=None):
    """主函数。"""
    rclpy.init(args=args)
    node = MapManagerNode()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
