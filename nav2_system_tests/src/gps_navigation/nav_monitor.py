#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
导航系统监控节点

持续监控Navigation2系统的关键组件状态，只有当所有必需的导航节点和服务都可用时，
才发布导航就绪事件，触发rviz2的启动。
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
from std_msgs.msg import Bool, String
from lifecycle_msgs.srv import GetState
import time


class NavMonitor(Node):
    def __init__(self):
        super().__init__('nav_monitor')

        # 参数
        self.declare_parameter('required_nodes', [
            '/controller_server',
            '/planner_server',
            '/smoother_server',
            '/behavior_server',
            '/bt_navigator',
            '/waypoint_follower',
            '/velocity_smoother',
            '/collision_monitor',
            '/lifecycle_manager_navigation'
        ])
        self.declare_parameter('required_actions', [
            '/compute_path_to_pose',
            '/follow_path',
            '/navigate_to_pose',
            '/navigate_through_poses',
            '/backup',
            '/spin',
            '/wait',
            '/drive_on_heading',
            '/assisted_teleop'
        ])
        self.declare_parameter('check_frequency', 1.0)
        self.declare_parameter('service_timeout', 2.0)

        self.required_nodes = self.get_parameter('required_nodes').value
        self.required_actions = self.get_parameter('required_actions').value
        self.check_frequency = self.get_parameter('check_frequency').value
        self.service_timeout = self.get_parameter('service_timeout').value

        # 导航系统状态跟踪
        self.node_status = {node: False for node in self.required_nodes}
        self.action_status = {action: False for action in self.required_actions}

        # 发布导航状态
        self.status_publisher = self.create_publisher(Bool, '/nav_ready', 10)
        self.detail_publisher = self.create_publisher(String, '/nav_status_detail', 10)

        # 状态标志
        self.all_nav_ready = False
        self.ready_event_published = False
        self.exit_when_ready = True  # 导航就绪时自动退出

        # 定时器 - 定期检查导航系统状态
        self.timer = self.create_timer(1.0 / self.check_frequency, self.check_navigation_status)

        # 生命周期状态客户端缓存
        self.lifecycle_clients = {}

        self.get_logger().info(f'Navigation monitor started.')
        self.get_logger().info(f'Required nodes: {len(self.required_nodes)}')
        self.get_logger().info(f'Required actions: {len(self.required_actions)}')
        self.get_logger().info(f'Check frequency: {self.check_frequency} Hz')

    def check_lifecycle_state(self, node_name):
        """检查生命周期节点的状态是否为 active"""
        try:
            # 确保节点名称以 '/' 开头
            if not node_name.startswith('/'):
                node_name = '/' + node_name

            # 创建或获取生命周期状态客户端
            if node_name not in self.lifecycle_clients:
                service_name = f'{node_name}/get_state'
                self.lifecycle_clients[node_name] = self.create_client(GetState, service_name)

            client = self.lifecycle_clients[node_name]

            # 检查服务是否可用（短超时）
            if not client.wait_for_service(timeout_sec=0.5):
                return False

            # 创建请求
            request = GetState.Request()

            # 同步调用服务（短超时）
            try:
                future = client.call_async(request)
                rclpy.spin_until_future_complete(self, future, timeout_sec=1.0)

                if future.done():
                    response = future.result()
                    # 检查状态是否为 active (状态ID为3)
                    return response.current_state.id == 3  # ACTIVE state
                else:
                    return False
            except Exception:
                return False

        except Exception as e:
            # 如果出现任何错误，假设节点不是生命周期节点或不可用
            return True  # 对于非生命周期节点，返回True

    def check_nodes(self):
        """检查所有必需的节点是否运行并且处于正确的生命周期状态"""
        node_names = self.get_node_names()

        for node in self.required_nodes:
            # 移除前导斜杠进行比较（与check_nav2_services.py保持一致）
            node_name = node.lstrip('/')
            is_running = node_name in node_names

            # 对于生命周期节点，还需要检查其状态是否为 active
            is_active = False
            if is_running:
                is_active = self.check_lifecycle_state(node)

            # 节点必须既运行又处于 active 状态才算就绪
            is_ready = is_running and is_active

            if is_ready and not self.node_status[node]:
                # 节点刚刚变为就绪状态
                self.node_status[node] = True
                self.get_logger().info(f'✅ Node {node} is now RUNNING and ACTIVE')
            elif not is_ready and self.node_status[node]:
                # 节点不再就绪
                self.node_status[node] = False
                if not is_running:
                    self.get_logger().warning(f'❌ Node {node} is NOT RUNNING')
                elif not is_active:
                    self.get_logger().warning(f'❌ Node {node} is RUNNING but NOT ACTIVE')

    def check_actions(self):
        """检查所有必需的动作服务器是否可用"""
        service_names = self.get_service_names_and_types()
        available_service_names = [name for name, _ in service_names]

        for action in self.required_actions:
            # 检查动作服务器是否存在（通过检查其相关服务）
            action_services = [
                f"{action}/_action/send_goal",
                f"{action}/_action/cancel_goal",
                f"{action}/_action/get_result"
            ]

            action_available = all(service in available_service_names for service in action_services)

            if action_available and not self.action_status[action]:
                # 动作服务器刚刚可用
                self.action_status[action] = True
                self.get_logger().info(f'✅ Action {action} is now AVAILABLE')
            elif not action_available and self.action_status[action]:
                # 动作服务器不可用
                self.action_status[action] = False
                self.get_logger().warning(f'❌ Action {action} is NOT AVAILABLE')

    def check_navigation_status(self):
        """定期检查导航系统状态"""
        # 检查节点状态
        self.check_nodes()

        # 检查动作服务器状态
        self.check_actions()

        # 检查是否所有组件都就绪
        all_nodes_ready = all(self.node_status.values())
        all_actions_ready = all(self.action_status.values())
        all_ready = all_nodes_ready and all_actions_ready

        if all_ready and not self.all_nav_ready:
            # 导航系统刚刚变为就绪状态
            self.all_nav_ready = True
            self.get_logger().info('✅ ALL NAVIGATION COMPONENTS ARE READY! Publishing ready event...')
            self.publish_ready_event()

            # 如果设置为导航就绪时退出，则退出节点
            if self.exit_when_ready:
                # 延迟退出，确保消息发布完成
                self.create_timer(1.0, self.exit_node)

        elif not all_ready and self.all_nav_ready:
            # 有组件失效
            self.all_nav_ready = False
            self.ready_event_published = False
            not_ready_nodes = [name for name, status in self.node_status.items() if not status]
            not_ready_actions = [name for name, status in self.action_status.items() if not status]

            if not_ready_nodes:
                self.get_logger().warning(f'Nodes not ready: {not_ready_nodes}')
            if not_ready_actions:
                self.get_logger().warning(f'Actions not ready: {not_ready_actions}')

        # 发布状态信息
        self.publish_status()

    def publish_ready_event(self):
        """发布导航就绪事件"""
        if not self.ready_event_published:
            # 发布布尔状态
            ready_msg = Bool()
            ready_msg.data = True
            self.status_publisher.publish(ready_msg)

            # 发布详细状态
            detail_msg = String()
            detail_msg.data = "ALL_NAVIGATION_COMPONENTS_READY"
            self.detail_publisher.publish(detail_msg)

            self.ready_event_published = True

            # 持续发布就绪状态，确保订阅者能收到
            for i in range(5):  # 发布5次确保可靠性
                self.status_publisher.publish(ready_msg)
                time.sleep(0.1)

    def publish_status(self):
        """发布当前导航系统状态"""
        # 发布布尔状态
        ready_msg = Bool()
        ready_msg.data = self.all_nav_ready
        self.status_publisher.publish(ready_msg)

        # 发布详细状态
        detail_msg = String()
        node_status_list = [f"{name.split('/')[-1]}:{'✓' if status else '✗'}"
                           for name, status in self.node_status.items()]
        action_status_list = [f"{name.split('/')[-1]}:{'✓' if status else '✗'}"
                             for name, status in self.action_status.items()]

        detail_msg.data = f"Nodes[{', '.join(node_status_list)}] Actions[{', '.join(action_status_list)}]"
        self.detail_publisher.publish(detail_msg)

    def exit_node(self):
        """退出节点以触发OnExecutionComplete事件"""
        self.get_logger().info('✅ Navigation monitor task completed. Exiting...')
        # 停止定时器
        if hasattr(self, 'timer'):
            self.timer.cancel()
        # 触发节点退出
        import sys
        sys.exit(0)


def main(args=None):
    rclpy.init(args=args)

    nav_monitor = NavMonitor()

    try:
        rclpy.spin(nav_monitor)
    except KeyboardInterrupt:
        pass
    finally:
        nav_monitor.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
