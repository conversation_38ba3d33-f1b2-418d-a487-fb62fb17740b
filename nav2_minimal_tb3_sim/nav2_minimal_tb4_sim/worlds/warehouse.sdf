<?xml version='1.0' encoding='ASCII'?>
<sdf version='1.7'>
  <world name='warehouse'>
    <physics name="1ms" type="ode">
      <max_step_size>0.003</max_step_size>
      <real_time_update_rate>1000.0</real_time_update_rate>
      <real_time_factor>1.0</real_time_factor>
    </physics>

    <plugin
      filename="gz-sim-physics-system"
      name="gz::sim::systems::Physics">
    </plugin>
    <plugin
      filename="gz-sim-user-commands-system"
      name="gz::sim::systems::UserCommands">
    </plugin>
    <plugin
      filename="gz-sim-scene-broadcaster-system"
      name="gz::sim::systems::SceneBroadcaster">
    </plugin>
    <plugin
      filename="gz-sim-sensors-system"
      name="gz::sim::systems::Sensors">
      <render_engine>ogre2</render_engine>
    </plugin>
    <plugin
      filename="gz-sim-imu-system"
      name="gz::sim::systems::Imu">
    </plugin>

    <scene>
      <ambient>1 1 1 1</ambient>
      <background>0.3 0.7 0.9 1</background>
      <shadows>0</shadows>
      <grid>false</grid>
    </scene>

    <model name='ground_plane'>
      <static>true</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0.0 0.0 1</normal>
              <size>1 1</size>
            </plane>
          </geometry>
        </collision>
      </link>
      <pose>0 0 0 0 0 0</pose>
    </model>

    <!-- Base -->
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/OpenRobotics/models/Warehouse
      </uri>
      <name>warehouse</name>
      <pose>0 0 -0.1 0 0 0</pose>
    </include>

    <!-- Pallet Box Mobile -->
    <!--
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/pallet_box_mobile
      </uri>
      <name>pallet_box_0</name>
      <pose>-4 12 0.01 0 0 0</pose>
      <static>true</static>
    </include> -->

    <!-- Shelves -->
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf_big
      </uri>
      <name>shelf_big_0</name>
      <pose>-8.5 -13 0 0 -0 0</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf_big
      </uri>
      <name>shelf_big_1</name>
      <pose>6.5 -13 0 0 -0 0</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf_big
      </uri>
      <name>shelf_big_2</name>
      <pose>-1.5 -13 0 0 -0 0</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_3</name>
      <pose>13.5 4.5 0 0 -0 0</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_4</name>
      <pose>10 4.5 0 0 -0 0</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_5</name>
      <pose>13.5 -21 0 0 -0 0</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_6</name>
      <pose>13.5 -15 0 0 -0 0</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_7</name>
      <pose>0.4 -2 0 0 -0 0</pose>
    </include>

    <!-- Navigation Challenge section -->

    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf_big
      </uri>
      <name>shelf_big_3</name>
      <pose>3.5 9.5 0 0 0 1.57</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf_big
      </uri>
      <name>shelf_big_4</name>
      <pose>-1.3 18.5 0 0 0 1.57</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_0</name>
      <pose>-10 21.5 0 0 -0 1.57</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_1</name>
      <pose>-7 23.6 0 0 -0 1.57</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/MovAi/models/shelf
      </uri>
      <name>shelf_2</name>
      <pose>-4 21.5 0 0 -0 1.57</pose>
    </include>

    <!-- Barriers -->

    <include>
      <uri>
      https://fuel.gazebosim.org/1.0/OpenRobotics/models/Jersey Barrier
      </uri>
      <name>barrier_0</name>
      <pose>-10.4 14.75 0 0 0 1.57</pose>
    </include>
    <include>
      <uri>
      https://fuel.gazebosim.org/1.0/OpenRobotics/models/Jersey Barrier
      </uri>
      <name>barrier_1</name>
      <pose>-10.4 10.5 0 0 0 1.57</pose>
    </include>
    <include>
      <uri>
      https://fuel.gazebosim.org/1.0/OpenRobotics/models/Jersey Barrier
      </uri>
      <name>barrier_2</name>
      <pose>-10.4 6.5 0 0 0 1.57</pose>
    </include>
    <include>
      <uri>
      https://fuel.gazebosim.org/1.0/OpenRobotics/models/Jersey Barrier
      </uri>
      <name>barrier_3</name>
      <pose>-12.85 4.85 0 0 0 0</pose>
    </include>

    <!-- Chairs -->

    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/OpenRobotics/models/Chair
      </uri>
      <name>chair_0</name>
      <pose>14.3 -5.5 0 0 -0 3</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/OpenRobotics/models/Chair
      </uri>
      <name>chair_1</name>
      <pose>14.3 -4 0 0 -0 -3</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/OpenRobotics/models/foldable_chair
      </uri>
      <name>fchair_0</name>
      <pose>-11.5 6.4 0 0 -0 -1.8</pose>
    </include>
    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/OpenRobotics/models/foldable_chair
      </uri>
      <name>fchair1</name>
      <pose>-14 6.5 0 0 -0 1.9</pose>
    </include>
    
    <!-- Table -->

    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/OpenRobotics/models/Table
      </uri>
      <name>table0</name>
      <pose>-12.7 6.5 0 0 0 0</pose>
    </include>

    <include>
      <uri>
      https://fuel.gazebosim.org/1.0/OpenRobotics/models/MaleVisitorOnPhone
      </uri>
      <name>Person 1 - Standing</name>
      <pose>1 -1 0 0 0 1.57</pose>
    </include>

    <include>
      <uri>
      https://fuel.gazebosim.org/1.0/plateau/models/Casual female
      </uri>
      <name>Person 2 - Walking</name>
      <pose>-12 15 0 0 0 0</pose>
    </include>

  </world>
</sdf>
