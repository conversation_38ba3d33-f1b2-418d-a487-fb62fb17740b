<?xml version="1.0"?>
<sdf version="1.6" xmlns:xacro="http://www.ros.org/wiki/xacro" xmlns:experimental="http://sdformat.org/schemas/experimental">
  <xacro:arg name="headless" default="true"/>
  <world name="depot">
    <scene>
      <grid>false</grid>
    </scene>
    <physics name="1ms" type="ode">
      <max_step_size>0.003</max_step_size>
      <real_time_update_rate>1000.0</real_time_update_rate>
      <real_time_factor>1.0</real_time_factor>
    </physics>

    <plugin
      filename="gz-sim-physics-system"
      name="gz::sim::systems::Physics">
    </plugin>
    <plugin
      filename="gz-sim-user-commands-system"
      name="gz::sim::systems::UserCommands">
    </plugin>
  <xacro:unless value="$(arg headless)">
    <plugin
      filename="gz-sim-scene-broadcaster-system"
      name="gz::sim::systems::SceneBroadcaster">
    </plugin>
  </xacro:unless>
    <plugin
      filename="gz-sim-sensors-system"
      name="gz::sim::systems::Sensors">
      <render_engine>ogre2</render_engine>
    </plugin>
    <plugin
      filename="gz-sim-imu-system"
      name="gz::sim::systems::Imu">
    </plugin>

    <include>
      <uri>
        https://fuel.gazebosim.org/1.0/OpenRobotics/models/Depot
      </uri>

      <!-- Remove annoying things that get in the way of visualizing the robot -->
      <experimental:params>
        <visual element_id="Fan1::visual" action="remove"/>
        <visual element_id="Fan2::visual" action="remove"/>
        <visual element_id="main::ROOF_visual" action="remove"/>
        <visual element_id="main::FANS_visual" action="remove"/>
      </experimental:params>

    </include>

  </world>
</sdf>
