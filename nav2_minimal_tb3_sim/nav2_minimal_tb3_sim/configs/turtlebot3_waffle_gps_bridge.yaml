# replace clock_bridge
- topic_name: "/clock"
  ros_type_name: "rosgraph_msgs/msg/Clock"
  gz_type_name: "gz.msgs.Clock"
  direction: GZ_TO_ROS

# no equivalent in TB3 - add
- topic_name: "joint_states"
  ros_type_name: "sensor_msgs/msg/JointState"
  gz_type_name: "gz.msgs.Model"
  direction: GZ_TO_ROS

# replace odom_bridge - check gz topic name
# gz topic published by DiffDrive plugin
- topic_name: "odom"
  ros_type_name: "nav_msgs/msg/Odometry"
  gz_type_name: "gz.msgs.Odometry"
  direction: GZ_TO_ROS

# replace odom_tf_bridge - check gz and ros topic names
# gz topic published by DiffDrive plugin
# prefix ros2 topic with gz
# 注释掉TF桥接，让robot_localization完全接管TF发布
# - topic_name: "tf"
#   ros_type_name: "tf2_msgs/msg/TFMessage"
#   gz_type_name: "gz.msgs.Pose_V"
#   direction: GZ_TO_ROS

# replace imu_bridge - check gz topic name
- topic_name: "imu/data"
  ros_type_name: "sensor_msgs/msg/Imu"
  gz_type_name: "gz.msgs.IMU"
  direction: GZ_TO_ROS

# replace lidar_bridge
- topic_name: "scan"
  ros_type_name: "sensor_msgs/msg/LaserScan"
  gz_type_name: "gz.msgs.LaserScan"
  direction: GZ_TO_ROS

# replace cmd_vel_bridge
- topic_name: "cmd_vel"
  ros_type_name: "geometry_msgs/msg/TwistStamped"
  gz_type_name: "gz.msgs.Twist"
  direction: ROS_TO_GZ

# replace navsat_bridge - check gz topic name
- topic_name: "gps/fix"
  ros_type_name: "sensor_msgs/msg/NavSatFix"
  gz_type_name: "gz.msgs.NavSat"
  direction: GZ_TO_ROS
