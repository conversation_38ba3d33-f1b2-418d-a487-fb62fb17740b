<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.83.0 commit date:2020-06-03, commit time:14:38, hash:211b6c29f771</authoring_tool>
    </contributor>
    <created>2020-06-29T17:22:52</created>
    <modified>2020-06-29T17:22:52</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_cameras>
    <camera id="Camera-camera" name="Camera">
      <optics>
        <technique_common>
          <perspective>
            <xfov sid="xfov">39.59775</xfov>
            <aspect_ratio>1.777778</aspect_ratio>
            <znear sid="znear">0.1</znear>
            <zfar sid="zfar">100</zfar>
          </perspective>
        </technique_common>
      </optics>
      <extra>
        <technique profile="blender">
          <shiftx sid="shiftx" type="float">0</shiftx>
          <shifty sid="shifty" type="float">0</shifty>
          <dof_distance sid="dof_distance" type="float">10</dof_distance>
        </technique>
      </extra>
    </camera>
  </library_cameras>
  <library_lights>
    <light id="Light-light" name="Light">
      <technique_common>
        <point>
          <color sid="color">1000 1000 1000</color>
          <constant_attenuation>1</constant_attenuation>
          <linear_attenuation>0</linear_attenuation>
          <quadratic_attenuation>0.00111109</quadratic_attenuation>
        </point>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">0</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">1</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">1</red>
          <green sid="green" type="float">1</green>
          <blue sid="blue" type="float">1</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1000</energy>
          <dist sid="blender_dist" type="float">29.99998</dist>
          <spotsize sid="spotsize" type="float">75</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">0.04999995</clipsta>
          <clipend sid="clipend" type="float">30.002</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <bufsize sid="bufsize" type="int">2880</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <area_shape sid="area_shape" type="int">1</area_shape>
          <area_size sid="area_size" type="float">0.1</area_size>
          <area_sizey sid="area_sizey" type="float">0.1</area_sizey>
          <area_sizez sid="area_sizez" type="float">1</area_sizez>
        </technique>
      </extra>
    </light>
  </library_lights>
  <library_effects>
    <effect id="Material-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="Material-material" name="Material">
      <instance_effect url="#Material-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cube_002-mesh" name="Cube.002">
      <mesh>
        <source id="Cube_002-mesh-positions">
          <float_array id="Cube_002-mesh-positions-array" count="48">-0.7789794 -1 -1 -1 -0.7789794 -1 -1 -0.7789794 1 -0.7789794 -1 1 -1 0.7789794 -1 -0.7789794 1 -1 -0.7789794 1 1 -1 0.7789794 1 1 -0.7789794 -1 0.7789794 -1 -1 0.7789794 -1 1 1 -0.7789794 1 0.7789794 1 -1 1 0.7789794 -1 1 0.7789794 1 0.7789794 1 1</float_array>
          <technique_common>
            <accessor source="#Cube_002-mesh-positions-array" count="16" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_002-mesh-normals">
          <float_array id="Cube_002-mesh-normals-array" count="30">0 -1 0 1 0 0 0 0 1 -1 0 0 0 1 0 -0.7071068 -0.7071068 0 -0.7071068 0.7071068 0 0.7071068 0.7071068 0 0.7071068 -0.7071068 0 0 0 -1</float_array>
          <technique_common>
            <accessor source="#Cube_002-mesh-normals-array" count="10" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_002-mesh-map-0">
          <float_array id="Cube_002-mesh-map-0-array" count="168">0.625 0.7776276 0.375 0.9723724 0.375 0.7776276 0.625 0.5276276 0.375 0.7223724 0.375 0.5276276 0.6526276 0.75 0.625 0.7223724 0.875 0.5276276 0.625 0.02762752 0.375 0.2223724 0.375 0.02762752 0.625 0.2776276 0.375 0.4723724 0.375 0.2776276 0.625 0.9723724 0.375 1 0.375 0.9723724 0.375 0.2776276 0.625 0.2223724 0.625 0.2776276 0.375 0.5276276 0.625 0.4723724 0.625 0.5276276 0.375 0.7776276 0.625 0.7223724 0.625 0.7776276 0.1526275 0.75 0.125 0.7223724 0.375 0.5276276 0.625 0.7776276 0.625 0.9723724 0.375 0.9723724 0.625 0.5276276 0.625 0.7223724 0.375 0.7223724 0.625 0.7223724 0.625 0.5276276 0.6526276 0.5 0.6526276 0.5 0.8473724 0.5 0.625 0.7223724 0.8473724 0.5 0.875 0.5276276 0.625 0.7223724 0.875 0.5276276 0.875 0.7223724 0.8473724 0.75 0.8473724 0.75 0.6526276 0.75 0.875 0.5276276 0.625 0.02762752 0.625 0.2223724 0.375 0.2223724 0.625 0.2776276 0.625 0.4723724 0.375 0.4723724 0.625 0.9723724 0.625 1 0.375 1 0.375 0.2776276 0.375 0.2223724 0.625 0.2223724 0.375 0.5276276 0.375 0.4723724 0.625 0.4723724 0.375 0.7776276 0.375 0.7223724 0.625 0.7223724 0.125 0.7223724 0.125 0.5276276 0.1526275 0.5 0.1526275 0.5 0.3473724 0.5 0.125 0.7223724 0.3473724 0.5 0.375 0.5276276 0.125 0.7223724 0.375 0.5276276 0.375 0.7223724 0.3473724 0.75 0.3473724 0.75 0.1526275 0.75 0.375 0.5276276</float_array>
          <technique_common>
            <accessor source="#Cube_002-mesh-map-0-array" count="84" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_002-mesh-vertices">
          <input semantic="POSITION" source="#Cube_002-mesh-positions"/>
        </vertices>
        <triangles count="28">
          <input semantic="VERTEX" source="#Cube_002-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_002-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_002-mesh-map-0" offset="2" set="0"/>
          <p>10 0 0 0 0 1 9 0 2 14 1 3 8 1 4 13 1 5 10 2 6 11 2 7 7 2 8 2 3 9 4 3 10 1 3 11 6 4 12 12 4 13 5 4 14 3 5 15 1 5 16 0 5 17 5 6 18 7 6 19 6 6 20 13 7 21 15 7 22 14 7 23 9 8 24 11 8 25 10 8 26 0 9 27 1 9 28 13 9 29 10 0 30 3 0 31 0 0 32 14 1 33 11 1 34 8 1 35 11 2 36 14 2 37 15 2 38 15 2 39 6 2 40 11 2 41 6 2 42 7 2 43 11 2 44 7 2 45 2 2 46 3 2 47 3 2 48 10 2 49 7 2 50 2 3 51 7 3 52 4 3 53 6 4 54 15 4 55 12 4 56 3 5 57 2 5 58 1 5 59 5 6 60 4 6 61 7 6 62 13 7 63 12 7 64 15 7 65 9 8 66 8 8 67 11 8 68 1 9 69 4 9 70 5 9 71 5 9 72 12 9 73 1 9 74 12 9 75 13 9 76 1 9 77 13 9 78 8 9 79 9 9 80 9 9 81 0 9 82 13 9 83</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Cube-mesh" name="Cube">
      <mesh>
        <source id="Cube-mesh-positions">
          <float_array id="Cube-mesh-positions-array" count="24">1 1 1 1 1 -1 1 -1 1 1 -1 -1 -1 1 1 -1 1 -1 -1 -1 1 -1 -1 -1</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube-mesh-normals">
          <float_array id="Cube-mesh-normals-array" count="18">0 0 1 0 -1 0 -1 0 0 0 0 -1 1 0 0 0 1 0</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-normals-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube-mesh-map-0">
          <float_array id="Cube-mesh-map-0-array" count="72">0.875 0.5 0.625 0.75 0.625 0.5 0.625 0.75 0.375 1 0.375 0.75 0.625 0 0.375 0.25 0.375 0 0.375 0.5 0.125 0.75 0.125 0.5 0.625 0.5 0.375 0.75 0.375 0.5 0.625 0.25 0.375 0.5 0.375 0.25 0.875 0.5 0.875 0.75 0.625 0.75 0.625 0.75 0.625 1 0.375 1 0.625 0 0.625 0.25 0.375 0.25 0.375 0.5 0.375 0.75 0.125 0.75 0.625 0.5 0.625 0.75 0.375 0.75 0.625 0.25 0.625 0.5 0.375 0.5</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube-mesh-vertices">
          <input semantic="POSITION" source="#Cube-mesh-positions"/>
        </vertices>
        <triangles material="Material-material" count="12">
          <input semantic="VERTEX" source="#Cube-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube-mesh-map-0" offset="2" set="0"/>
          <p>4 0 0 2 0 1 0 0 2 2 1 3 7 1 4 3 1 5 6 2 6 5 2 7 7 2 8 1 3 9 7 3 10 5 3 11 0 4 12 3 4 13 1 4 14 4 5 15 1 5 16 5 5 17 4 0 18 6 0 19 2 0 20 2 1 21 6 1 22 7 1 23 6 2 24 4 2 25 5 2 26 1 3 27 3 3 28 7 3 29 0 4 30 2 4 31 3 4 32 4 5 33 0 5 34 1 5 35</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Cube_001" name="Cube.001" type="NODE">
        <matrix sid="transform">132.5 0 0 0 0 132.5 0 0 0 0 46 46.12724 0 0 0 1</matrix>
        <instance_geometry url="#Cube_002-mesh" name="Cube.001"/>
      </node>
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6859207 -0.3240135 0.6515582 7.358891 0.7276763 0.3054208 -0.6141704 -6.925791 0 0.8953956 0.4452714 4.958309 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Light" name="Light" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Light-light"/>
      </node>
      <node id="Cube" name="Cube" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube-mesh" name="Cube">
          <bind_material>
            <technique_common>
              <instance_material symbol="Material-material" target="#Material-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>