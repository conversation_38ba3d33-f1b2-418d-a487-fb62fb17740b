<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

<xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/common_properties.urdf.xacro"/>

<xacro:macro name="tower_standoff" params="name parent_link:=shell_link *origin">

  <xacro:property name="link_name" value="${name}"/>
  <xacro:property name="joint_name" value="${name}_joint"/>

  <xacro:property name="mass" value="0.260"/>

  <joint name="${joint_name}" type="fixed">
    <parent link="${parent_link}"/>
    <child link="${link_name}"/>
    <xacro:insert_block name="origin"/>
  </joint>

  <link name="${link_name}">
    <visual>
      <origin rpy="0 ${pi/2} 0"/>
      <geometry>
       <mesh filename="package://nav2_minimal_tb4_description/meshes/tower_standoff.dae" scale="1 1 1" />
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.006" length="0.205"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="${mass}"/>
      <inertia ixx="0.00000019604" ixy="0.0" ixz="0.0"
	             iyy="0.00005262313" iyz="0.0" izz="0.00005262313" />
    </inertial>
  </link>

  <gazebo reference="${joint_name}">
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>

  <gazebo reference="${link_name}">
    <xacro:material_black/>
  </gazebo>

</xacro:macro>

</robot>
