<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

<xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/common_properties.urdf.xacro"/>

<xacro:macro name="tower_sensor_plate" params="name parent_link:=shell_link *origin">

  <xacro:property name="link_name" value="${name}"/>
  <xacro:property name="joint_name" value="${name}_joint"/>

  <xacro:property name="mass" value="0.332"/>

  <joint name="${joint_name}" type="fixed">
    <parent link="${parent_link}"/>
    <child link="${link_name}"/>
    <xacro:insert_block name="origin"/>
  </joint>

  <link name="${link_name}">
    <visual>
      <geometry>
       <mesh filename="package://nav2_minimal_tb4_description/meshes/tower_sensor_plate.dae" scale="1 1 1" />
      </geometry>
    </visual>
    <collision>
      <origin xyz="0 0 0.001"/>
      <geometry>
        <cylinder radius="0.137" length="0.003"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="${mass}"/>
      <inertia ixx="0.00092982299" ixy="0.0" ixz="0.0"
	             iyy="0.00083033498" iyz="0.0" izz="0.00175985947" />
    </inertial>
  </link>

  <gazebo reference="${joint_name}">
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>

  <gazebo reference="${link_name}">
    <xacro:material_black/>
  </gazebo>

</xacro:macro>

</robot>
