<?xml version="1.0" ?>
<robot name="create3" xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/bumper.urdf.xacro" />
  <xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/caster.urdf.xacro" />
  <xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/common_properties.urdf.xacro"/>
  <xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/sensors/imu.urdf.xacro" />
  <xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/wheel.urdf.xacro" />

  <!-- Gazebo version -->
  <xacro:arg name="gazebo"                       default="ignition" />

  <!-- Namespace -->
  <xacro:arg name="namespace"                    default=""/>

  <!-- Mechanical properties -->
  <xacro:property name="body_z_offset"           value="${-2.5*cm2m}" />
  <xacro:property name="body_collision_z_offset" value="${1*cm2m}" />
  <xacro:property name="body_mass"               value="2.300" />
  <xacro:property name="body_radius"             value="${16.4*cm2m}" />
  <xacro:property name="body_length"             value="${6*cm2m}" />

  <xacro:property name="body_cog_x_offset"       value="${2.28*cm2m}" />

  <xacro:property name="bumper_mass"             value="0.1" />
  <xacro:property name="bumper_offset_z"         value="${-2.5*cm2m}" />
  <xacro:property name="bumper_inertial_x"       value="${8*cm2m}" />
  <xacro:property name="bumper_inertial_z"       value="${2*cm2m}"/>

  <xacro:property name="wheel_height"            value="${-2.75*cm2m}" />
  <xacro:property name="distance_between_wheels" value="${23.3*cm2m}" />

  <xacro:property name="caster_position_x"       value="${12.5*cm2m}" />
  <xacro:property name="caster_position_z"       value="${-5.03*cm2m}" />
  
  <xacro:property name="wheel_drop_offset_z"     value="${3.5*mm2m}"/>
  <xacro:property name="wheel_drop_z"            value="${wheel_height + wheel_drop_offset_z}"/>

  <xacro:property name="base_link_z_offset"     value="${6.42*cm2m}"/>

  <!-- Create 3 base definition-->
  <link name="base_link">
    <visual>
      <origin xyz="0 0 ${body_z_offset + base_link_z_offset}" rpy="0 0 ${pi/2}"/>
      <geometry>
        <mesh filename="package://nav2_minimal_tb4_description/meshes/body_visual.dae" />
      </geometry>
    </visual>
    <collision name="create3_base_collision">
      <origin xyz="0 0 ${body_z_offset + body_collision_z_offset  + base_link_z_offset}" rpy="0 0 ${pi/2}"/>
      <geometry>
        <cylinder length="${body_length}" radius="${body_radius}"/>
      </geometry>
    </collision>
    <xacro:inertial_cylinder_with_pose mass="${body_mass}" radius="${body_radius}" length="${body_length}">
      <origin xyz="${body_cog_x_offset} 0 ${body_collision_z_offset + base_link_z_offset}"/>
    </xacro:inertial_cylinder_with_pose>
  </link>

  <gazebo reference="base_link">
    <xacro:material_darkgray/>
  </gazebo>

  <!-- Bumper -->
  <xacro:bumper
      gazebo="$(arg gazebo)"
      namespace="$(arg namespace)"
      visual_mesh="package://nav2_minimal_tb4_description/meshes/bumper_visual.dae"
      collision_mesh="package://nav2_minimal_tb4_description/meshes/bumper_collision.dae">
    <origin xyz="0 0 ${bumper_offset_z  + base_link_z_offset}"/>
    <inertial>
      <origin xyz="${bumper_inertial_x} 0 ${bumper_inertial_z}"/>
      <mass value="${bumper_mass}"/>
      <inertia ixx="0.0013483753405" ixy="0.0000000454352" ixz="0.0000014434849"
               iyy="0.0002521736852" iyz="-0.0000000006721" izz="0.0015442525386"/>
    </inertial>
  </xacro:bumper>

  <!-- Wheels with mechanical wheel drop -->
  <xacro:wheel name="left" gazebo="$(arg gazebo)">
    <origin xyz="0 ${distance_between_wheels/2} ${wheel_drop_z  + base_link_z_offset}" rpy="${-pi/2} 0 0"/>
  </xacro:wheel>

  <xacro:wheel name="right" gazebo="$(arg gazebo)">
    <origin xyz="0 ${-distance_between_wheels/2} ${wheel_drop_z  + base_link_z_offset}" rpy="${-pi/2} 0 0"/>
  </xacro:wheel>

  <!-- Caster wheel -->
  <xacro:caster name="front_caster" parent_link="base_link">
    <origin xyz="${caster_position_x} 0 ${caster_position_z  + base_link_z_offset}" rpy="${-pi/2} 0 0"/>
  </xacro:caster>

  <!-- IMU -->
  <xacro:imu_sensor gazebo="$(arg gazebo)" namespace="$(arg namespace)">
    <origin xyz="0.050613 0.043673 ${0.0202 + base_link_z_offset}"/>
  </xacro:imu_sensor>

  <gazebo>
      <plugin
        filename="gz-sim-diff-drive-system"
        name="gz::sim::systems::DiffDrive">
        <left_joint>left_wheel_joint</left_joint>
        <right_joint>right_wheel_joint</right_joint>
        <wheel_separation>0.233</wheel_separation>
        <wheel_radius>0.03575</wheel_radius>
        <max_linear_acceleration>2</max_linear_acceleration>
        <min_linear_acceleration>-2</min_linear_acceleration>
        <max_angular_acceleration>3</max_angular_acceleration>
        <min_angular_acceleration>-3</min_angular_acceleration>
        <max_linear_velocity>0.5</max_linear_velocity>
        <min_linear_velocity>-0.5</min_linear_velocity>
        <max_angular_velocity>2.0</max_angular_velocity>
        <min_angular_velocity>-2.0</min_angular_velocity>
        <topic>/cmd_vel</topic>
        <odom_topic>/odom</odom_topic>
        <tf_topic>tf</tf_topic>
        <frame_id>odom</frame_id>
        <child_frame_id>base_link</child_frame_id>
        <odom_publish_frequency>30</odom_publish_frequency>
      </plugin>

      <plugin
        filename="gz-sim-joint-state-publisher-system"
        name="gz::sim::systems::JointStatePublisher">
        <joint_name>left_wheel_joint</joint_name>
        <joint_name>right_wheel_joint</joint_name>
        <topic>joint_states</topic>
        <update_rate>30</update_rate>
      </plugin>
  </gazebo>

</robot>
