<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

<xacro:macro name="rplidar" params="name parent_link gazebo *origin">
  <xacro:include filename="$(find nav2_minimal_tb4_description)/urdf/icreate/common_properties.urdf.xacro"/>

  <xacro:property name="mass"       value="0.17"/>
  <xacro:property name="length_x"   value="${7.1*cm2m}" />
  <xacro:property name="length_y"   value="${10*cm2m}" />
  <xacro:property name="length_z"   value="${6*cm2m}" />

  <xacro:property name="collision_x_offset"       value="${0*cm2m}" />
  <xacro:property name="collision_y_offset"       value="${1.3*cm2m}" />
  <xacro:property name="collision_z_offset"       value="${-1.9*cm2m}" />

  <joint name="${name}_joint" type="fixed">
    <parent link="${parent_link}"/>
    <child link="${name}_link"/>
    <xacro:insert_block name="origin"/>
  </joint>

  <link name="${name}_link">
    <visual>
      <geometry>
        <mesh filename="package://nav2_minimal_tb4_description/meshes/rplidar.dae" scale="1 1 1" />
      </geometry>
    </visual>
    <collision>
      <origin xyz="${collision_x_offset} ${collision_y_offset} ${collision_z_offset}"/>
      <geometry>
        <box size="${length_x} ${length_y} ${length_z}"/>
      </geometry>
    </collision>
    <xacro:inertial_cuboid mass="0.17" x="${length_x}" y="${length_y}" z="${length_z}"/>
  </link>

  <gazebo reference="${name}_link">
    <xacro:ray_sensor sensor_name="${name}" gazebo="${gazebo}" 
                  update_rate="10.0" visualize="true" 
                  h_samples="360" h_res="1.0" h_min_angle="0.000" h_max_angle="6.280000" 
                  r_min="0.164" r_max="20.0" r_res="0.01">
                  <plugin name="dummy" filename="dummyfile"></plugin>
    </xacro:ray_sensor>
    <xacro:material_darkgray/>
  </gazebo>

  <gazebo reference="${name}_joint">
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>

</xacro:macro>
</robot>
