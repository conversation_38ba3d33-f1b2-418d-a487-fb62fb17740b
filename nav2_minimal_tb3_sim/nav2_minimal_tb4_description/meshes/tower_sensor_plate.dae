<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor/>
    <created>2022-03-23T14:01:05.789524</created>
    <modified>2022-03-23T14:01:05.789527</modified>
    <unit name="meter" meter="1.0"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="effect_tower_sensor_plate" name="effect_tower_sensor_plate">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color>0.0 0.0 0.0 1.0</color>
            </emission>
            <ambient>
              <color>0.0 0.0 0.0 1.0</color>
            </ambient>
            <diffuse>
              <color>0.101 0.101 0.101 1.0</color>
            </diffuse>
            <specular>
              <color>1 1 1 1.0</color>
            </specular>
            <shininess>
              <float>0.0</float>
            </shininess>
            <reflective>
              <color>0.0 0.0 0.0 1.0</color>
            </reflective>
            <reflectivity>
              <float>0.0</float>
            </reflectivity>
            <transparent>
              <color>0.0 0.0 0.0 1.0</color>
            </transparent>
            <transparency>
              <float>1.0</float>
            </transparency>
          </phong>
        </technique>
        <extra>
          <technique profile="GOOGLEEARTH">
            <double_sided>0</double_sided>
          </technique>
        </extra>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_geometries>
    <geometry id="geometry0" name="tower_sensor_plate">
      <mesh>
        <source id="cubeverts-array0">
          <float_array count="492" id="cubeverts-array0-array">-0.1345843 -0.01058583 0 -0.1345843 -0.01058583 0.003 -0.1345843 0.01058583 0 -0.1345843 0.01058583 0.003 -0.1312742 -0.03149714 0 -0.1312742 -0.03149714 0.003 -0.1312742 0.03149714 0 -0.1312742 0.03149714 0.003 -0.1247355 -0.05163379 0 -0.1247355 -0.05163379 0.003 -0.1247355 0.05163379 0 -0.1247355 0.05163379 0.003 -0.115129 -0.07050051 0 -0.115129 -0.07050051 0.003 -0.115129 0.07050051 0 -0.115129 0.07050051 0.003 -0.1026908 -0.08763329 0 -0.1026908 -0.08763329 0.003 -0.1026908 0.08763329 0 -0.1026908 0.08763329 0.003 -0.08772704 -0.1026107 0 -0.08772704 -0.1026107 0.003 -0.08772704 0.1026107 0 -0.08772704 0.1026107 0.003 -0.07831945 -0.09062618 0 -0.07831945 -0.09062618 0.003 -0.07831945 0.09065604 0 -0.07831945 0.09065604 0.003 -0.07801801 -0.09175118 0 -0.07801801 -0.09175118 0.003 -0.07801801 -0.08950118 0 -0.07801801 -0.08950118 0.003 -0.07801801 0.08953104 0 -0.07801801 0.08953104 0.003 -0.07801801 0.09178104 0 -0.07801801 0.09178104 0.003 -0.07719445 -0.09257474 0 -0.07719445 -0.09257474 0.003 -0.07719445 -0.08867762 0 -0.07719445 -0.08867762 0.003 -0.07719445 0.08870748 0 -0.07719445 0.08870748 0.003 -0.07719445 0.0926046 0 -0.07719445 0.0926046 0.003 -0.07606945 -0.09287618 0 -0.07606945 -0.09287618 0.003 -0.07606945 -0.08837618 0 -0.07606945 -0.08837618 0.003 -0.07606945 0.08840604 0 -0.07606945 0.08840604 0.003 -0.07606945 0.09290604 0 -0.07606945 0.09290604 0.003 -0.07494445 -0.09257474 0 -0.07494445 -0.09257474 0.003 -0.07494445 -0.08867762 0 -0.07494445 -0.08867762 0.003 -0.07494445 0.08870748 0 -0.07494445 0.08870748 0.003 -0.07494445 0.0926046 0 -0.07494445 0.0926046 0.003 -0.0741209 -0.09175118 0 -0.0741209 -0.09175118 0.003 -0.0741209 -0.08950118 0 -0.0741209 -0.08950118 0.003 -0.0741209 0.08953104 0 -0.0741209 0.08953104 0.003 -0.0741209 0.09178104 0 -0.0741209 0.09178104 0.003 -0.07381945 -0.09062618 0 -0.07381945 -0.09062618 0.003 -0.07381945 0.09065604 0 -0.07381945 0.09065604 0.003 -0.07060562 -0.1150645 0 -0.07060562 -0.1150645 0.003 -0.07060562 0.1150645 0 -0.07060562 0.1150645 0.003 -0.05174768 -0.1246883 0 -0.05174768 -0.1246883 0.003 -0.05174768 0.1246883 0 -0.05174768 0.1246883 0.003 -0.03161702 -0.1312454 0 -0.03161702 -0.1312454 0.003 -0.03161702 0.1312454 0 -0.03161702 0.1312454 0.003 -0.01070874 -0.1345746 0 -0.01070874 -0.1345746 0.003 -0.01070874 0.1345746 0 -0.01070874 0.1345746 0.003 0.01046292 -0.1345939 0 0.01046292 -0.1345939 0.003 0.01046292 0.1345939 0 0.01046292 0.1345939 0.003 0.02837944 -0.1142808 0 0.02837944 -0.1142808 0.003 0.02837944 0.1143106 0 0.02837944 0.1143106 0.003 0.02868088 -0.1154058 0 0.02868088 -0.1154058 0.003 0.02868088 -0.1131558 0 0.02868088 -0.1131558 0.003 0.02868088 0.1131856 0 0.02868088 0.1131856 0.003 0.02868088 0.1154356 0 0.02868088 0.1154356 0.003 0.02950444 -0.1162293 0 0.02950444 -0.1162293 0.003 0.02950444 -0.1123322 0 0.02950444 -0.1123322 0.003 0.02950444 0.1123621 0 0.02950444 0.1123621 0.003 0.02950444 0.1162592 0 0.02950444 0.1162592 0.003 0.03062944 -0.1165308 0 0.03062944 -0.1165308 0.003 0.03062944 -0.1120308 0 0.03062944 -0.1120308 0.003 0.03062944 0.1120606 0 0.03062944 0.1120606 0.003 0.03062944 0.1165606 0 0.03062944 0.1165606 0.003 0.03137724 -0.131303 0 0.03137724 -0.131303 0.003 0.03137724 0.131303 0 0.03137724 0.131303 0.003 0.03175444 -0.1162293 0 0.03175444 -0.1162293 0.003 0.03175444 -0.1123322 0 0.03175444 -0.1123322 0.003 0.03175444 0.1123621 0 0.03175444 0.1123621 0.003 0.03175444 0.1162592 0 0.03175444 0.1162592 0.003 0.032578 -0.1154058 0 0.032578 -0.1154058 0.003 0.032578 -0.1131558 0 0.032578 -0.1131558 0.003 0.032578 0.1131856 0 0.032578 0.1131856 0.003 0.032578 0.1154356 0 0.032578 0.1154356 0.003 0.03287944 -0.1142808 0 0.03287944 -0.1142808 0.003 0.03287944 0.1143106 0 0.03287944 0.1143106 0.003 0.05151985 -0.1247826 0 0.05151985 -0.1247826 0.003 0.05151985 0.1247826 0 0.05151985 0.1247826 0.003 0.07039533 -0.1151933 0 0.07039533 -0.1151933 0.003 0.07039533 0.1151933 0 0.07039533 0.1151933 0.003 0.08753947 -0.1027708 0 0.08753947 -0.1027708 0.003 0.08753947 0.1027708 0 0.08753947 0.1027708 0.003 0.1025306 -0.08782071 0 0.1025306 -0.08782071 0.003 0.1025306 0.08782071 0 0.1025306 0.08782071 0.003 0.115 -0.07071068 0 0.115 -0.07071068 0.003 0.115 0.07071068 0 0.115 0.07071068 0.003</float_array>
          <technique_common>
            <accessor count="164" source="#cubeverts-array0-array" stride="3">
              <param type="float" name="X"/>
              <param type="float" name="Y"/>
              <param type="float" name="Z"/>
            </accessor>
          </technique_common>
        </source>
        <source id="cubenormals-array0">
          <float_array count="1020" id="cubenormals-array0-array">0.8081572 0.5889668 0 0.8081572 0.5889668 0 0.7061377 0.7080746 0 0.7061377 0.7080746 0 0.58675 0.8097681 0 0.58675 0.8097681 0 0.4529325 0.8915448 0 0.4529325 0.8915448 0 0.3079745 0.9513946 0 0.3079745 0.9513946 0 0.1554422 0.987845 0 0.1554422 0.987845 0 -0.000913149 0.9999996 0 -0.000913149 0.9999996 0 -0.1572462 0.9875594 0 -0.1572462 0.9875594 0 -0.3097121 0.9508304 0 -0.3097121 0.9508304 0 -0.4545599 0.8907162 0 -0.4545599 0.8907162 0 -0.5882285 0.8086948 0 -0.5882285 0.8086948 0 -0.7074294 0.706784 0 -0.7074294 0.706784 0 -0.8092317 0.5874897 0 -0.8092317 0.5874897 0 -0.891131 0.4537461 0 -0.891131 0.4537461 0 -0.951113 0.308843 0 -0.951113 0.308843 0 -0.9877025 0.1563445 0 -0.9877025 0.1563445 0 -1 0 0 -1 0 0 -0.9877025 -0.1563445 0 -0.9877025 -0.1563445 -0 -0.951113 -0.308843 0 -0.951113 -0.308843 -0 -0.891131 -0.4537461 0 -0.891131 -0.4537461 -0 -0.8092317 -0.5874897 0 -0.8092317 -0.5874897 -0 -0.7074294 -0.706784 0 -0.7074294 -0.706784 -0 -0.5882285 -0.8086948 0 -0.5882285 -0.8086948 -0 -0.4545599 -0.8907162 0 -0.4545599 -0.8907162 -0 -0.3097121 -0.9508304 0 -0.3097121 -0.9508304 -0 -0.1572462 -0.9875594 0 -0.1572462 -0.9875594 -0 -0.000913149 -0.9999996 0 -0.000913149 -0.9999996 -0 0.1554422 -0.987845 0 0.1554422 -0.987845 0 0.3079745 -0.9513946 0 0.3079745 -0.9513946 0 0.4529325 -0.8915448 0 0.4529325 -0.8915448 0 0.58675 -0.8097681 0 0.58675 -0.8097681 0 0.7061377 -0.7080746 0 0.7061377 -0.7080746 0 0.8081572 -0.5889668 0 0.8081572 -0.5889668 0 0 0 1 0 0 1 0 -0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0 0 1 -0 0 1 0 0 1 0 -0 1 0 -0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0 0 1 0 0 1 0 0 1 -0 -0 1 -0 0 1 -0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0 0 1 0 0 1 0 0 1 -0 0 1 0 0 1 -0 0 1 0 0 1 -0 0 1 0 0 1 -0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0 0 1 0 0 1 -0 0 1 0 0 1 -0 0 1 -0 0 1 0 -0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 -0 1 -0 0 1 0 -0 1 -0 0 1 -0 0 1 0 0 1 0 0 1 0 0 1 0 -0 1 0 0 1 0 0 1 -0 0 1 0 0 1 -0 0 1 0 0 1 -0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0 0 1 -0 0 1 0 0 -1 -0 0 -1 -0 0 -1 0 0 -1 -0 0 -1 0 -0 -1 0 -0 -1 0 0 -1 -0 0 -1 0 0 -1 0 0 -1 -0 0 -1 0 0 -1 0 0 -1 -0 0 -1 0 0 -1 -0 0 -1 -0 0 -1 -0 0 -1 0 -0 -1 0 -0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 -0 0 -1 0 -0 -1 0 -0 -1 0 0 -1 0 -0 -1 0 0 -1 0 0 -1 0 -0 -1 0 -0 -1 0 -0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 -0 0 -1 0 0 -1 0 -0 -1 -0 -0 -1 0 -0 -1 -0 -0 -1 -0 -0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 -0 0 -1 0 0 -1 -0 0 -1 0 0 -1 -0 0 -1 0 0 -1 0 0 -1 0 -0 -1 0 0 -1 -0 0 -1 0 0 -1 -0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 -0 0 -1 0 -0 -1 0 0 -1 0 -0 -1 0 0 -1 0 0 -1 -0.9659253 -0.2588208 0 -0.9659253 -0.2588208 -0 -0.7071068 -0.7071068 0 -0.7071068 -0.7071068 -0 -0.2588208 -0.9659253 0 -0.2588208 -0.9659253 -0 0.2588208 -0.9659253 0 0.2588208 -0.9659253 0 0.7071035 -0.70711 0 0.7071035 -0.70711 0 0.965927 -0.2588147 0 0.965927 -0.2588147 0 0.965927 0.2588147 0 0.965927 0.2588147 0 0.7071068 0.7071068 0 0.7071068 0.7071068 0 0.2588147 0.965927 0 0.2588147 0.965927 0 -0.2588147 0.965927 0 -0.2588147 0.965927 0 -0.70711 0.7071035 0 -0.70711 0.7071035 0 -0.9659253 0.2588208 0 -0.9659253 0.2588208 0 -0.9659261 -0.2588177 0 -0.9659261 -0.2588177 -0 -0.707106 -0.7071076 0 -0.707106 -0.7071076 -0 -0.2588204 -0.9659255 0 -0.2588204 -0.9659255 -0 0.2588208 -0.9659253 0 0.2588208 -0.9659253 0 0.707106 -0.7071076 0 0.707106 -0.7071076 0 0.9659258 -0.2588193 0 0.9659258 -0.2588193 0 0.9659258 0.2588193 0 0.9659258 0.2588193 0 0.707106 0.7071076 0 0.707106 0.7071076 0 0.2588208 0.9659253 0 0.2588208 0.9659253 0 -0.2588204 0.9659255 0 -0.2588204 0.9659255 0 -0.707106 0.7071076 0 -0.707106 0.7071076 0 -0.9659261 0.2588177 0 -0.9659261 0.2588177 0 -0.9659261 -0.2588177 0 -0.9659261 -0.2588177 -0 -0.7071092 -0.7071043 0 -0.7071092 -0.7071043 -0 -0.2588081 -0.9659287 0 -0.2588081 -0.9659287 -0 0.2588086 -0.9659286 0 0.2588086 -0.9659286 0 0.7071092 -0.7071043 0 0.7071092 -0.7071043 0 0.9659258 -0.2588193 0 0.9659258 -0.2588193 0 0.9659258 0.2588193 0 0.9659258 0.2588193 0 0.707106 0.7071076 0 0.707106 0.7071076 0 0.2588208 0.9659253 0 0.2588208 0.9659253 0 -0.2588204 0.9659255 0 -0.2588204 0.9659255 0 -0.707106 0.7071076 0 -0.707106 0.7071076 0 -0.9659261 0.2588177 0 -0.9659261 0.2588177 0 -0.9659253 -0.2588208 0 -0.9659253 -0.2588208 -0 -0.70711 -0.7071035 0 -0.70711 -0.7071035 -0 -0.2588147 -0.965927 0 -0.2588147 -0.965927 -0 0.2588147 -0.965927 0 0.2588147 -0.965927 0 0.7071068 -0.7071068 0 0.7071068 -0.7071068 0 0.965927 -0.2588147 0 0.965927 -0.2588147 0 0.965927 0.2588147 0 0.965927 0.2588147 0 0.7071035 0.70711 0 0.7071035 0.70711 0 0.2588208 0.9659253 0 0.2588208 0.9659253 0 -0.2588208 0.9659253 0 -0.2588208 0.9659253 0 -0.7071068 0.7071068 0 -0.7071068 0.7071068 0 -0.9659253 0.2588208 0 -0.9659253 0.2588208 0 1 0 0 1 0 0</float_array>
          <technique_common>
            <accessor count="340" source="#cubenormals-array0-array" stride="3">
              <param type="float" name="X"/>
              <param type="float" name="Y"/>
              <param type="float" name="Z"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="cubeverts-array0-vertices">
          <input semantic="POSITION" source="#cubeverts-array0"/>
        </vertices>
        <triangles count="340" material="ref_tower_sensor_plate">
          <input offset="0" semantic="VERTEX" source="#cubeverts-array0-vertices"/>
          <input offset="1" semantic="NORMAL" source="#cubenormals-array0"/>
          <p>158 0 163 0 162 0 159 1 163 1 158 1 154 2 159 2 158 2 155 3 159 3 154 3 150 4 155 4 154 4 151 5 155 5 150 5 146 6 151 6 150 6 147 7 151 7 146 7 122 8 147 8 146 8 123 9 147 9 122 9 90 10 123 10 122 10 91 11 123 11 90 11 86 12 91 12 90 12 87 13 91 13 86 13 82 14 87 14 86 14 83 15 87 15 82 15 78 16 83 16 82 16 79 17 83 17 78 17 74 18 79 18 78 18 75 19 79 19 74 19 22 20 75 20 74 20 23 21 75 21 22 21 18 22 23 22 22 22 19 23 23 23 18 23 14 24 19 24 18 24 15 25 19 25 14 25 10 26 15 26 14 26 11 27 15 27 10 27 6 28 11 28 10 28 7 29 11 29 6 29 2 30 7 30 6 30 3 31 7 31 2 31 0 32 3 32 2 32 1 33 3 33 0 33 4 34 1 34 0 34 5 35 1 35 4 35 8 36 5 36 4 36 9 37 5 37 8 37 12 38 9 38 8 38 13 39 9 39 12 39 16 40 13 40 12 40 17 41 13 41 16 41 20 42 17 42 16 42 21 43 17 43 20 43 72 44 21 44 20 44 73 45 21 45 72 45 76 46 73 46 72 46 77 47 73 47 76 47 80 48 77 48 76 48 81 49 77 49 80 49 84 50 81 50 80 50 85 51 81 51 84 51 88 52 85 52 84 52 89 53 85 53 88 53 120 54 89 54 88 54 121 55 89 55 120 55 144 56 121 56 120 56 145 57 121 57 144 57 148 58 145 58 144 58 149 59 145 59 148 59 152 60 149 60 148 60 153 61 149 61 152 61 156 62 153 62 152 62 157 63 153 63 156 63 160 64 157 64 156 64 161 65 157 65 160 65 57 66 39 66 47 66 163 67 159 67 55 67 63 68 163 68 55 68 133 69 121 69 145 69 155 70 137 70 159 70 57 71 47 71 55 71 159 72 57 72 55 72 65 73 57 73 159 73 71 74 65 74 159 74 127 75 135 75 157 75 115 76 127 76 157 76 57 77 49 77 25 77 31 78 57 78 25 78 39 79 57 79 31 79 141 80 133 80 145 80 149 81 141 81 145 81 135 82 141 82 149 82 153 83 135 83 149 83 157 84 135 84 153 84 43 85 23 85 35 85 69 86 107 86 115 86 157 87 69 87 115 87 63 88 69 88 157 88 161 89 63 89 157 89 163 90 63 90 161 90 73 91 77 91 53 91 45 92 73 92 53 92 21 93 73 93 45 93 75 94 23 94 43 94 95 95 79 95 75 95 69 96 61 96 93 96 99 97 69 97 93 97 107 98 69 98 99 98 121 99 133 99 125 99 113 100 121 100 125 100 89 101 121 101 113 101 105 102 89 102 113 102 85 103 89 103 105 103 97 104 85 104 105 104 81 105 85 105 97 105 93 106 81 106 97 106 77 107 81 107 93 107 61 108 77 108 93 108 53 109 77 109 61 109 143 110 137 110 155 110 151 111 143 111 155 111 139 112 143 112 151 112 147 113 139 113 151 113 131 114 139 114 147 114 123 115 131 115 147 115 119 116 131 116 123 116 159 117 137 117 129 117 117 118 159 118 129 118 71 119 159 119 117 119 27 120 35 120 23 120 19 121 27 121 23 121 33 122 27 122 19 122 15 123 33 123 19 123 41 124 33 124 15 124 11 125 41 125 15 125 7 126 41 126 11 126 111 127 119 127 123 127 91 128 111 128 123 128 103 129 111 129 91 129 87 130 103 130 91 130 95 131 103 131 87 131 83 132 95 132 87 132 79 133 95 133 83 133 41 134 7 134 3 134 1 135 41 135 3 135 49 136 41 136 1 136 5 137 49 137 1 137 71 138 117 138 109 138 101 139 71 139 109 139 67 140 71 140 101 140 95 141 67 141 101 141 59 142 67 142 95 142 75 143 59 143 95 143 51 144 59 144 75 144 43 145 51 145 75 145 21 146 45 146 37 146 29 147 21 147 37 147 17 148 21 148 29 148 25 149 17 149 29 149 13 150 17 150 25 150 49 151 13 151 25 151 9 152 13 152 49 152 5 153 9 153 49 153 92 154 44 154 98 154 106 155 98 155 44 155 36 156 20 156 28 156 72 157 20 157 36 157 114 158 106 158 44 158 52 159 114 159 44 159 60 160 114 160 52 160 72 161 36 161 44 161 92 162 72 162 44 162 76 163 72 163 92 163 80 164 76 164 92 164 48 165 2 165 40 165 50 166 22 166 74 166 84 167 80 167 92 167 96 168 84 168 92 168 88 169 84 169 96 169 104 170 88 170 96 170 120 171 88 171 104 171 160 172 156 172 56 172 64 173 160 173 56 173 70 174 160 174 64 174 90 175 122 175 118 175 110 176 90 176 118 176 86 177 90 177 110 177 102 178 86 178 110 178 82 179 86 179 102 179 94 180 82 180 102 180 78 181 82 181 94 181 74 182 78 182 94 182 56 183 156 183 152 183 148 184 56 184 152 184 144 185 56 185 148 185 120 186 104 186 112 186 124 187 120 187 112 187 144 188 120 188 124 188 132 189 144 189 124 189 94 190 100 190 70 190 66 191 94 191 70 191 74 192 94 192 66 192 58 193 74 193 66 193 50 194 74 194 58 194 150 195 154 195 136 195 142 196 150 196 136 196 146 197 150 197 142 197 138 198 146 198 142 198 122 199 146 199 138 199 130 200 122 200 138 200 118 201 122 201 130 201 70 202 100 202 108 202 116 203 70 203 108 203 160 204 70 204 116 204 128 205 160 205 116 205 162 206 160 206 128 206 136 207 162 207 128 207 158 208 162 208 136 208 154 209 158 209 136 209 134 210 126 210 56 210 144 211 134 211 56 211 140 212 134 212 144 212 132 213 140 213 144 213 114 214 60 214 68 214 62 215 114 215 68 215 54 216 114 216 62 216 22 217 50 217 42 217 34 218 22 218 42 218 18 219 22 219 34 219 26 220 18 220 34 220 14 221 18 221 26 221 32 222 14 222 26 222 10 223 14 223 32 223 40 224 10 224 32 224 6 225 10 225 40 225 2 226 6 226 40 226 24 227 28 227 20 227 16 228 24 228 20 228 30 229 24 229 16 229 12 230 30 230 16 230 38 231 30 231 12 231 8 232 38 232 12 232 4 233 38 233 8 233 38 234 4 234 0 234 2 235 38 235 0 235 46 236 38 236 2 236 48 237 46 237 2 237 54 238 46 238 48 238 56 239 54 239 48 239 114 240 54 240 56 240 126 241 114 241 56 241 66 242 70 242 71 242 67 243 66 243 71 243 58 244 66 244 67 244 59 245 58 245 67 245 50 246 58 246 59 246 51 247 50 247 59 247 42 248 50 248 51 248 43 249 42 249 51 249 34 250 42 250 43 250 35 251 34 251 43 251 26 252 34 252 35 252 27 253 26 253 35 253 32 254 26 254 27 254 33 255 32 255 27 255 40 256 32 256 33 256 41 257 40 257 33 257 48 258 40 258 41 258 49 259 48 259 41 259 56 260 48 260 49 260 57 261 56 261 49 261 64 262 56 262 57 262 65 263 64 263 57 263 70 264 64 264 65 264 71 265 70 265 65 265 138 266 142 266 143 266 139 267 138 267 143 267 130 268 138 268 139 268 131 269 130 269 139 269 118 270 130 270 131 270 119 271 118 271 131 271 110 272 118 272 119 272 111 273 110 273 119 273 102 274 110 274 111 274 103 275 102 275 111 275 94 276 102 276 103 276 95 277 94 277 103 277 100 278 94 278 95 278 101 279 100 279 95 279 108 280 100 280 101 280 109 281 108 281 101 281 116 282 108 282 109 282 117 283 116 283 109 283 128 284 116 284 117 284 129 285 128 285 117 285 136 286 128 286 129 286 137 287 136 287 129 287 142 288 136 288 137 288 143 289 142 289 137 289 134 290 140 290 141 290 135 291 134 291 141 291 126 292 134 292 135 292 127 293 126 293 135 293 114 294 126 294 127 294 115 295 114 295 127 295 106 296 114 296 115 296 107 297 106 297 115 297 98 298 106 298 107 298 99 299 98 299 107 299 92 300 98 300 99 300 93 301 92 301 99 301 96 302 92 302 93 302 97 303 96 303 93 303 104 304 96 304 97 304 105 305 104 305 97 305 112 306 104 306 105 306 113 307 112 307 105 307 124 308 112 308 113 308 125 309 124 309 113 309 132 310 124 310 125 310 133 311 132 311 125 311 140 312 132 312 133 312 141 313 140 313 133 313 62 314 68 314 69 314 63 315 62 315 69 315 54 316 62 316 63 316 55 317 54 317 63 317 46 318 54 318 55 318 47 319 46 319 55 319 38 320 46 320 47 320 39 321 38 321 47 321 30 322 38 322 39 322 31 323 30 323 39 323 24 324 30 324 31 324 25 325 24 325 31 325 28 326 24 326 25 326 29 327 28 327 25 327 36 328 28 328 29 328 37 329 36 329 29 329 44 330 36 330 37 330 45 331 44 331 37 331 52 332 44 332 45 332 53 333 52 333 45 333 60 334 52 334 53 334 61 335 60 335 53 335 68 336 60 336 61 336 69 337 68 337 61 337 161 338 160 338 162 338 163 339 161 339 162 339</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_materials>
    <material id="mat_tower_sensor_plate" name="tower_sensor_plate">
      <instance_effect url="#effect_tower_sensor_plate"/>
    </material>
  </library_materials>
  <library_visual_scenes>
    <visual_scene id="myscene">
      <node id="node0" name="node0">
        <instance_geometry url="#geometry0">
          <bind_material>
            <technique_common>
              <instance_material symbol="ref_tower_sensor_plate" target="#mat_tower_sensor_plate"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#myscene"/>
  </scene>
</COLLADA>
