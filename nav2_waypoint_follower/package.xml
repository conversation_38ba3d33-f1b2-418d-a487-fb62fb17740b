<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_waypoint_follower</name>
  <version>1.3.1</version>
  <description>A waypoint follower navigation server</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>

  <depend>cv_bridge</depend>
  <depend>geographic_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>image_transport</depend>
  <depend>nav2_core</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_util</depend>
  <depend>nav_msgs</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_action</depend>
  <depend>rclcpp_components</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>robot_localization</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>tf2_ros</depend>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <nav2_waypoint_follower plugin="${prefix}/plugins.xml" />
  </export>
</package>
