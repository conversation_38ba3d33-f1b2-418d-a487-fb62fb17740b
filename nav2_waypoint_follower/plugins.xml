<class_libraries>
  <library path="wait_at_waypoint">
    <class type="nav2_waypoint_follower::WaitAtWaypoint" base_class_type="nav2_core::WaypointTaskExecutor">
      <description>Let's robot sleep for a specified amount of time at waypoint arrival</description>
    </class>
  </library>
  <library path="photo_at_waypoint">
    <class type="nav2_waypoint_follower::PhotoAtWaypoint" base_class_type="nav2_core::WaypointTaskExecutor">
      <description>Run-time plugin that takes photos at waypoint arrivals when using waypoint follower node.
        Saves the taken photos to specified directory.</description>
    </class>
  </library>
  <library path="input_at_waypoint">
    <class type="nav2_waypoint_follower::InputAtWaypoint" base_class_type="nav2_core::WaypointTaskExecutor">
      <description>Let's robot wait for input at waypoint arrival</description>
    </class>
  </library>
</class_libraries>
