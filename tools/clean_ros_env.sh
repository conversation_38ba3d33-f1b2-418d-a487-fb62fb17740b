#!/bin/bash
# 清理ROS2/Gazebo/RViz等相关进程，适用于每次测试前环境清理

set -e

# 终止常见仿真相关进程（不杀python3，防止断开devcontainer）
killall -9 ros gz rviz2 robot_state_publisher component_container_isolated map_server lifecycle_manager nav2_bringup turtlebot3_waffle foxglove_bridge rqt static_transform_publisher 2>/dev/null || true

# 精准查找并杀死ROS/Gazebo/RViz相关进程，排除vscode/cursor等远程开发进程
ps aux | grep -E "ros2|gz|rviz|robot_state_publisher|component_container|map_server|lifecycle_manager|nav2|turtlebot|foxglove|rqt|static_transform_publisher" | \
  grep -v grep | grep -v vscode | grep -v cursor | awk '{print $2}' | xargs -r kill -9 || true

# 特别处理static_transform_publisher
ps aux | grep static_transform_publisher | grep -v grep | awk '{print $2}' | xargs -r kill -9 || true

# 停止ros2 daemon
ros2 daemon stop || true

# 保存当前 ROS 2 环境变量
SAVED_ROS_DOMAIN_ID=$ROS_DOMAIN_ID
SAVED_RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION

# 暂时重置 ROS 2 环境变量以清理环境
unset ROS_DOMAIN_ID
unset RMW_IMPLEMENTATION

# 重新启动 ROS 2 daemon
ros2 daemon start

# 输出剩余话题
source /opt/overlay_ws/install/setup.bash
ros2 topic list

echo "环境清理完成。"

# 如果还有 /tf_static 话题，提示用户手动重启终端
if ros2 topic list | grep -q "/tf_static"; then
  echo "警告：/tf_static 话题仍然存在。如果需要完全清除，请尝试重新启动终端或者重启容器。"
fi

# 恢复 ROS 2 环境变量
if [ -n "$SAVED_ROS_DOMAIN_ID" ]; then
  export ROS_DOMAIN_ID=$SAVED_ROS_DOMAIN_ID
  echo "恢复 ROS_DOMAIN_ID=$ROS_DOMAIN_ID"
fi

if [ -n "$SAVED_RMW_IMPLEMENTATION" ]; then
  export RMW_IMPLEMENTATION=$SAVED_RMW_IMPLEMENTATION
  echo "恢复 RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION"
fi