<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_util</name>
  <version>1.3.1</version>
  <description>Nav2 utilities</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>
  <license>BSD-3-Clause</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>nav2_common</depend>

  <depend>bond</depend>
  <depend>bondcpp</depend>
  <depend>builtin_interfaces</depend>
  <depend>geometry_msgs</depend>
  <depend>lifecycle_msgs</depend>
  <depend>nav2_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>rcl_interfaces</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_action</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>std_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>tf2_msgs</depend>
  <depend>tf2_ros</depend>

  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>std_srvs</test_depend>
  <test_depend>test_msgs</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
