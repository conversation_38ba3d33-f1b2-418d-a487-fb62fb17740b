<class_libraries>
  <library path="layers">
    <class type="nav2_costmap_2d::InflationLayer"  base_class_type="nav2_costmap_2d::Layer">
      <description>Inflates obstacles to speed collision checking and to make robot prefer to stay away from obstacles.</description>
    </class>
    <class type="nav2_costmap_2d::ObstacleLayer"   base_class_type="nav2_costmap_2d::Layer">
      <description>Listens to laser scan and point cloud messages and marks and clears grid cells.</description>
    </class>
    <class type="nav2_costmap_2d::StaticLayer"     base_class_type="nav2_costmap_2d::Layer">
      <description>Listens to OccupancyGrid messages and copies them in, like from map_server.</description>
    </class>
    <class type="nav2_costmap_2d::VoxelLayer"     base_class_type="nav2_costmap_2d::Layer">
      <description>Similar to obstacle costmap, but uses 3D voxel grid to store data.</description>
    </class>
    <class type="nav2_costmap_2d::RangeSensorLayer" base_class_type="nav2_costmap_2d::Layer">
      <description>A range-sensor (sonar, IR) based obstacle layer for costmap_2d</description>
    </class>
    <class type="nav2_costmap_2d::DenoiseLayer" base_class_type="nav2_costmap_2d::Layer">
      <description>Filters noise-induced freestanding obstacles or small obstacles groups</description>
    </class>
    <class type="nav2_costmap_2d::PluginContainerLayer" base_class_type="nav2_costmap_2d::Layer">
      <description>Plugin to group different layers within the same costmap</description>
    </class>
  </library>

  <library path="filters">
    <class type="nav2_costmap_2d::KeepoutFilter"   base_class_type="nav2_costmap_2d::Layer">
      <description>Prevents the robot from appearing in keepout zones marked on map.</description>
    </class>
    <class type="nav2_costmap_2d::SpeedFilter"     base_class_type="nav2_costmap_2d::Layer">
      <description>Restricts maximum speed of robot in speed-limit zones marked on map.</description>
    </class>
    <class type="nav2_costmap_2d::BinaryFilter"    base_class_type="nav2_costmap_2d::Layer">
      <description>Binary flip filter.</description>
    </class>
  </library>
</class_libraries>
