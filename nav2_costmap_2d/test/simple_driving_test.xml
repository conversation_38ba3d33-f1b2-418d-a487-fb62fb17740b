<launch>
  <param name="/use_sim_time" value="true"/>

  <node name="rosplay" pkg="rosbag" type="play"
        args="-s 5 -r 1 --clock --hz=10 $(find nav2_costmap_2d)/test/simple_driving_test_indexed.bag" />

  <node name="map_server" pkg="map_server" type="map_server" args="$(find nav2_costmap_2d)/test/willow-full-0.025.pgm 0.025" />

  <rosparam file="$(find nav2_costmap_2d)/test/costmap_params.yaml" command="load" ns="simple_driving_test/test_costmap" />
  <test time-limit="600" test-name="simple_driving_test" pkg="nav2_costmap_2d" type="costmap_tester">
    <param name="wait_time" value="40.0" />
  </test>

</launch>
